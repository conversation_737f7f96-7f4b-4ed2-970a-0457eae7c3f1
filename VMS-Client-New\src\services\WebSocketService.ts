/**
 * Dynamic WebSocket Service with automatic server discovery and reconnection
 */

import { io, Socket } from 'socket.io-client';
import { serverDiscovery, VMSServerInfo } from './ServerDiscovery';

export interface WebSocketConfig {
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
  timeout?: number;
}

export type WebSocketEventHandler = (data: any) => void;

class DynamicWebSocketService {
  private socket: Socket | null = null;
  private currentServer: VMSServerInfo | null = null;
  private config: Required<WebSocketConfig>;
  private eventHandlers = new Map<string, WebSocketEventHandler[]>();
  private isConnected = false;
  private isConnecting = false;
  private reconnectAttempts = 0;

  constructor(config: WebSocketConfig = {}) {
    this.config = {
      reconnectionAttempts: config.reconnectionAttempts || 10,
      reconnectionDelay: config.reconnectionDelay || 2000,
      timeout: config.timeout || 10000,
    };
  }

  /**
   * Initialize WebSocket connection with server discovery
   */
  async connect(): Promise<boolean> {
    if (this.isConnected || this.isConnecting) {
      return this.isConnected;
    }

    this.isConnecting = true;

    try {
      // Discover servers if not already done
      if (!this.currentServer) {
        const discovery = await serverDiscovery.discoverServers();
        if (!discovery.bestServer) {
          throw new Error('No VMS servers found for WebSocket connection');
        }
        this.currentServer = discovery.bestServer;
      }

      await this.createSocketConnection();
      return this.isConnected;

    } catch (error) {
      console.error('❌ Failed to connect WebSocket:', error);
      return false;
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * Create socket connection to current server
   */
  private async createSocketConnection(): Promise<void> {
    if (!this.currentServer) {
      throw new Error('No server configured for WebSocket');
    }

    const socketUrl = this.currentServer.url;
    console.log(`🔌 Connecting WebSocket to: ${socketUrl}`);

    this.socket = io(socketUrl, {
      transports: ['polling', 'websocket'],
      upgrade: true,
      rememberUpgrade: false,
      timeout: this.config.timeout,
      reconnection: true,
      reconnectionAttempts: this.config.reconnectionAttempts,
      reconnectionDelay: this.config.reconnectionDelay,
      withCredentials: true,
      autoConnect: true,
    });

    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not created'));
        return;
      }

      // Connection successful
      this.socket.on('connect', () => {
        console.log('✅ WebSocket connected successfully');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.setupEventHandlers();
        resolve();
      });

      // Connection error
      this.socket.on('connect_error', (error) => {
        console.error('❌ WebSocket connection error:', error);
        this.isConnected = false;
        
        if (this.reconnectAttempts === 0) {
          reject(error);
        }
      });

      // Disconnection
      this.socket.on('disconnect', (reason) => {
        console.warn('🔌 WebSocket disconnected:', reason);
        this.isConnected = false;
        
        // Try to rediscover servers if disconnected unexpectedly
        if (reason === 'io server disconnect' || reason === 'transport close') {
          this.handleServerDisconnection();
        }
      });

      // Reconnection attempts
      this.socket.on('reconnect_attempt', (attemptNumber) => {
        console.log(`🔄 WebSocket reconnection attempt ${attemptNumber}...`);
        this.reconnectAttempts = attemptNumber;
      });

      // Successful reconnection
      this.socket.on('reconnect', (attemptNumber) => {
        console.log(`✅ WebSocket reconnected after ${attemptNumber} attempts`);
        this.isConnected = true;
        this.reconnectAttempts = 0;
      });

      // Failed to reconnect
      this.socket.on('reconnect_failed', () => {
        console.error('❌ WebSocket failed to reconnect');
        this.isConnected = false;
        this.handleServerDisconnection();
      });
    });
  }

  /**
   * Handle server disconnection by trying to rediscover
   */
  private async handleServerDisconnection(): Promise<void> {
    try {
      console.log('🔍 Server disconnected, attempting to rediscover...');
      
      const discovery = await serverDiscovery.discoverServers();
      
      if (discovery.bestServer && discovery.bestServer.url !== this.currentServer?.url) {
        console.log(`🔄 Found new server: ${discovery.bestServer.url}`);
        this.currentServer = discovery.bestServer;
        
        // Disconnect current socket and create new one
        if (this.socket) {
          this.socket.disconnect();
        }
        
        await this.createSocketConnection();
      }
    } catch (error) {
      console.error('Failed to rediscover servers for WebSocket:', error);
    }
  }

  /**
   * Setup event handlers for socket events
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Setup custom event handlers
    this.eventHandlers.forEach((handlers, event) => {
      handlers.forEach(handler => {
        this.socket!.on(event, handler);
      });
    });
  }

  /**
   * Add event listener
   */
  on(event: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event)!.push(handler);
    
    // If socket is already connected, add the handler immediately
    if (this.socket && this.isConnected) {
      this.socket.on(event, handler);
    }
  }

  /**
   * Remove event listener
   */
  off(event: string, handler?: WebSocketEventHandler): void {
    if (handler) {
      const handlers = this.eventHandlers.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
      
      if (this.socket) {
        this.socket.off(event, handler);
      }
    } else {
      // Remove all handlers for this event
      this.eventHandlers.delete(event);
      if (this.socket) {
        this.socket.off(event);
      }
    }
  }

  /**
   * Emit event to server
   */
  emit(event: string, data?: any): void {
    if (this.socket && this.isConnected) {
      this.socket.emit(event, data);
    } else {
      console.warn(`Cannot emit ${event}: WebSocket not connected`);
    }
  }

  /**
   * Join a room
   */
  joinRoom(room: string): void {
    this.emit('join-room', { room });
  }

  /**
   * Leave a room
   */
  leaveRoom(room: string): void {
    this.emit('leave-room', { room });
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.isConnecting = false;
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    connected: boolean;
    connecting: boolean;
    server: VMSServerInfo | null;
    reconnectAttempts: number;
  } {
    return {
      connected: this.isConnected,
      connecting: this.isConnecting,
      server: this.currentServer,
      reconnectAttempts: this.reconnectAttempts,
    };
  }

  /**
   * Test WebSocket connection
   */
  async testConnection(): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.socket || !this.isConnected) {
        resolve(false);
        return;
      }

      const timeout = setTimeout(() => {
        resolve(false);
      }, 5000);

      this.socket.emit('ping', { timestamp: Date.now() });
      
      this.socket.once('pong', () => {
        clearTimeout(timeout);
        resolve(true);
      });
    });
  }
}

// Create singleton instance
export const webSocketService = new DynamicWebSocketService();

export default webSocketService;
