@echo off
echo Building VMS Client for .NET 8 LTS...
echo.

REM Clean previous builds
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo Restoring NuGet packages...
dotnet restore

echo Building application...
dotnet build --configuration Release

echo Publishing self-contained executable...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "publish\win-x64"

echo.
echo Build completed!
echo.
echo Executable location: publish\win-x64\VMS-Client-NET8.exe
echo.
pause
