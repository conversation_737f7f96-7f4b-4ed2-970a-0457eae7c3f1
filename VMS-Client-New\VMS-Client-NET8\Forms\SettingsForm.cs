using VMS.Client.Services;

namespace VMS.Client.Forms;

public partial class SettingsForm : Form
{
    private readonly IConfigurationService _configService;
    
    private CheckBox _autoDiscoveryCheckBox;
    private NumericUpDown _timeoutNumeric;
    private CheckBox _minimizeToTrayCheckBox;
    private CheckBox _startMinimizedCheckBox;
    private TextBox _preferredServerTextBox;
    private Button _okButton;
    private Button _cancelButton;
    private Button _resetButton;

    public SettingsForm(IConfigurationService configService)
    {
        _configService = configService;
        InitializeComponent();
        LoadSettings();
    }

    private void InitializeComponent()
    {
        SuspendLayout();
        
        Text = "VMS Client Settings";
        Size = new Size(400, 350);
        StartPosition = FormStartPosition.CenterParent;
        FormBorderStyle = FormBorderStyle.FixedDialog;
        MaximizeBox = false;
        MinimizeBox = false;
        
        // Connection settings group
        var connectionGroup = new GroupBox
        {
            Text = "Connection Settings",
            Location = new Point(12, 12),
            Size = new Size(360, 120)
        };
        
        _autoDiscoveryCheckBox = new CheckBox
        {
            Text = "Enable automatic server discovery",
            Location = new Point(15, 25),
            Size = new Size(250, 24),
            AutoSize = true
        };
        
        var timeoutLabel = new Label
        {
            Text = "Connection timeout (ms):",
            Location = new Point(15, 55),
            Size = new Size(150, 23),
            AutoSize = true
        };
        
        _timeoutNumeric = new NumericUpDown
        {
            Location = new Point(170, 52),
            Size = new Size(80, 23),
            Minimum = 1000,
            Maximum = 30000,
            Increment = 1000
        };
        
        var preferredServerLabel = new Label
        {
            Text = "Preferred server:",
            Location = new Point(15, 85),
            Size = new Size(100, 23),
            AutoSize = true
        };
        
        _preferredServerTextBox = new TextBox
        {
            Location = new Point(120, 82),
            Size = new Size(200, 23),
            PlaceholderText = "e.g., 10.25.42.216:8080"
        };
        
        connectionGroup.Controls.AddRange(new Control[]
        {
            _autoDiscoveryCheckBox, timeoutLabel, _timeoutNumeric,
            preferredServerLabel, _preferredServerTextBox
        });
        
        // UI settings group
        var uiGroup = new GroupBox
        {
            Text = "User Interface",
            Location = new Point(12, 145),
            Size = new Size(360, 80)
        };
        
        _minimizeToTrayCheckBox = new CheckBox
        {
            Text = "Minimize to system tray",
            Location = new Point(15, 25),
            Size = new Size(200, 24),
            AutoSize = true
        };
        
        _startMinimizedCheckBox = new CheckBox
        {
            Text = "Start minimized",
            Location = new Point(15, 50),
            Size = new Size(150, 24),
            AutoSize = true
        };
        
        uiGroup.Controls.AddRange(new Control[]
        {
            _minimizeToTrayCheckBox, _startMinimizedCheckBox
        });
        
        // Buttons
        _okButton = new Button
        {
            Text = "OK",
            Location = new Point(135, 280),
            Size = new Size(75, 30),
            DialogResult = DialogResult.OK
        };
        
        _cancelButton = new Button
        {
            Text = "Cancel",
            Location = new Point(220, 280),
            Size = new Size(75, 30),
            DialogResult = DialogResult.Cancel
        };
        
        _resetButton = new Button
        {
            Text = "Reset to Defaults",
            Location = new Point(12, 280),
            Size = new Size(110, 30)
        };
        
        // Add controls
        Controls.AddRange(new Control[]
        {
            connectionGroup, uiGroup,
            _okButton, _cancelButton, _resetButton
        });
        
        AcceptButton = _okButton;
        CancelButton = _cancelButton;
        
        // Event handlers
        _okButton.Click += OnOkClick;
        _resetButton.Click += OnResetClick;
        
        ResumeLayout(false);
        PerformLayout();
    }

    private void LoadSettings()
    {
        _autoDiscoveryCheckBox.Checked = _configService.GetSetting("AutoDiscovery", "true") == "true";
        _timeoutNumeric.Value = int.Parse(_configService.GetSetting("DefaultTimeout", "5000"));
        _minimizeToTrayCheckBox.Checked = _configService.GetSetting("MinimizeToTray", "false") == "true";
        _startMinimizedCheckBox.Checked = _configService.GetSetting("StartMinimized", "false") == "true";
        _preferredServerTextBox.Text = _configService.GetSetting("PreferredServer", "");
    }

    private void SaveSettings()
    {
        _configService.SetSetting("AutoDiscovery", _autoDiscoveryCheckBox.Checked.ToString().ToLower());
        _configService.SetSetting("DefaultTimeout", _timeoutNumeric.Value.ToString());
        _configService.SetSetting("MinimizeToTray", _minimizeToTrayCheckBox.Checked.ToString().ToLower());
        _configService.SetSetting("StartMinimized", _startMinimizedCheckBox.Checked.ToString().ToLower());
        _configService.SetSetting("PreferredServer", _preferredServerTextBox.Text.Trim());
        
        _configService.SaveSettings();
    }

    private void OnOkClick(object? sender, EventArgs e)
    {
        SaveSettings();
    }

    private void OnResetClick(object? sender, EventArgs e)
    {
        var result = MessageBox.Show(
            "This will reset all settings to their default values.\n\nAre you sure you want to continue?",
            "Reset Settings",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question
        );
        
        if (result == DialogResult.Yes)
        {
            _autoDiscoveryCheckBox.Checked = true;
            _timeoutNumeric.Value = 5000;
            _minimizeToTrayCheckBox.Checked = false;
            _startMinimizedCheckBox.Checked = false;
            _preferredServerTextBox.Text = "";
        }
    }
}
