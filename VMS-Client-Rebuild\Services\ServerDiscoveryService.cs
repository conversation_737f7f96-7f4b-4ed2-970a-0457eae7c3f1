using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace VMS_Client.Services
{
    public class ServerDiscoveryService
    {
        private UdpClient? _udpClient;
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isDiscovering = false;

        public event EventHandler<ServerFoundEventArgs>? ServerFound;
        public event EventHandler? DiscoveryFailed;

        public async void StartDiscovery()
        {
            if (_isDiscovering) return;

            _isDiscovering = true;
            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Listen for UDP broadcasts on port 45454
                await ListenForBroadcasts(_cancellationTokenSource.Token);
            }
            catch (Exception)
            {
                DiscoveryFailed?.Invoke(this, EventArgs.Empty);
            }
            finally
            {
                _isDiscovering = false;
            }
        }

        public void StopDiscovery()
        {
            _cancellationTokenSource?.Cancel();
            _udpClient?.Close();
            _udpClient?.Dispose();
            _isDiscovering = false;
        }

        private async Task ListenForBroadcasts(CancellationToken cancellationToken)
        {
            _udpClient = new UdpClient(45454);
            _udpClient.EnableBroadcast = true;

            // Set timeout for discovery
            var timeoutTask = Task.Delay(10000, cancellationToken); // 10 second timeout

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var receiveTask = _udpClient.ReceiveAsync();
                    var completedTask = await Task.WhenAny(receiveTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        // Timeout - try sending discovery request
                        await SendDiscoveryRequest();
                        await Task.Delay(2000, cancellationToken); // Wait 2 seconds for response
                        continue;
                    }

                    var result = await receiveTask;
                    var message = Encoding.UTF8.GetString(result.Buffer);

                    try
                    {
                        var announcement = JsonConvert.DeserializeObject<ServiceAnnouncement>(message);
                        
                        if (announcement?.Type == "VMS_SERVICE_ANNOUNCEMENT" && 
                            announcement.Service != null &&
                            announcement.LocalIPs != null && 
                            announcement.LocalIPs.Length > 0)
                        {
                            var serverUrl = $"http://{announcement.LocalIPs[0]}:{announcement.Service.Port}";
                            
                            // Verify server is actually responding
                            if (await VerifyServerConnection(serverUrl))
                            {
                                ServerFound?.Invoke(this, new ServerFoundEventArgs(serverUrl));
                                return;
                            }
                        }
                    }
                    catch (JsonException)
                    {
                        // Ignore invalid JSON messages
                        continue;
                    }
                }
            }
            catch (ObjectDisposedException)
            {
                // UDP client was disposed, ignore
            }
            catch (SocketException)
            {
                // Network error, trigger discovery failed
                DiscoveryFailed?.Invoke(this, EventArgs.Empty);
            }
        }

        private async Task SendDiscoveryRequest()
        {
            try
            {
                using var client = new UdpClient();
                client.EnableBroadcast = true;

                var request = new { type = "VMS_DISCOVERY_REQUEST" };
                var requestJson = JsonConvert.SerializeObject(request);
                var requestBytes = Encoding.UTF8.GetBytes(requestJson);

                // Send to broadcast address
                await client.SendAsync(requestBytes, requestBytes.Length, new IPEndPoint(IPAddress.Broadcast, 45455));
                
                // Also try specific network ranges
                var localIP = GetLocalIPAddress();
                if (localIP != null)
                {
                    var networkBase = GetNetworkBase(localIP);
                    var broadcastIP = GetBroadcastAddress(localIP);
                    
                    if (broadcastIP != null)
                    {
                        await client.SendAsync(requestBytes, requestBytes.Length, new IPEndPoint(broadcastIP, 45455));
                    }
                }
            }
            catch (Exception)
            {
                // Ignore send errors
            }
        }

        private async Task<bool> VerifyServerConnection(string serverUrl)
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(3);
                
                var response = await httpClient.GetAsync($"{serverUrl}/health");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private IPAddress? GetLocalIPAddress()
        {
            try
            {
                using var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, 0);
                socket.Connect("*******", 65530);
                var endPoint = socket.LocalEndPoint as IPEndPoint;
                return endPoint?.Address;
            }
            catch
            {
                return null;
            }
        }

        private string GetNetworkBase(IPAddress ip)
        {
            var bytes = ip.GetAddressBytes();
            return $"{bytes[0]}.{bytes[1]}.{bytes[2]}";
        }

        private IPAddress? GetBroadcastAddress(IPAddress ip)
        {
            try
            {
                var bytes = ip.GetAddressBytes();
                bytes[3] = 255; // Assume /24 network
                return new IPAddress(bytes);
            }
            catch
            {
                return null;
            }
        }
    }

    public class ServerFoundEventArgs : EventArgs
    {
        public string ServerUrl { get; }

        public ServerFoundEventArgs(string serverUrl)
        {
            ServerUrl = serverUrl;
        }
    }

    public class ServiceAnnouncement
    {
        [JsonProperty("type")]
        public string? Type { get; set; }

        [JsonProperty("service")]
        public ServiceInfo? Service { get; set; }

        [JsonProperty("localIPs")]
        public string[]? LocalIPs { get; set; }
    }

    public class ServiceInfo
    {
        [JsonProperty("serviceName")]
        public string? ServiceName { get; set; }

        [JsonProperty("host")]
        public string? Host { get; set; }

        [JsonProperty("port")]
        public int Port { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }

        [JsonProperty("capabilities")]
        public string[]? Capabilities { get; set; }
    }
}
