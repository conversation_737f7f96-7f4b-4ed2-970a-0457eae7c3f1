{"name": "vms-client-dynamic", "version": "1.0.0", "description": "Dynamic VMS Client with Automatic Server Discovery", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000 --host 0.0.0.0"}, "dependencies": {"axios": "^1.6.0", "socket.io-client": "^4.7.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "lucide-react": "^0.300.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "sonner": "^1.3.0", "date-fns": "^2.30.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.0", "vite": "^5.0.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}}