using VMS.Client.Models;

namespace VMS.Client.Services;

public interface IServerDiscoveryService
{
    /// <summary>
    /// Discover VMS servers on the network
    /// </summary>
    Task<DiscoveryResult> DiscoverServersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Test connection to a specific server
    /// </summary>
    Task<VmsServerInfo?> TestServerAsync(string host, int port, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cached discovered servers
    /// </summary>
    List<VmsServerInfo> GetCachedServers();

    /// <summary>
    /// Clear discovery cache
    /// </summary>
    void ClearCache();

    /// <summary>
    /// Event fired when servers are discovered
    /// </summary>
    event EventHandler<DiscoveryResult>? ServersDiscovered;

    /// <summary>
    /// Event fired when discovery status changes
    /// </summary>
    event EventHandler<string>? StatusChanged;
}

public interface IVmsApiService
{
    Task<bool> TestConnectionAsync(string baseUrl, CancellationToken cancellationToken = default);
    Task<ServerHealthInfo?> GetHealthInfoAsync(string baseUrl, CancellationToken cancellationToken = default);
}

public interface INetworkService
{
    Task<List<string>> GetLocalNetworkRangesAsync();
    Task<bool> PingHostAsync(string host, int timeoutMs = 2000);
    List<string> GetCommonServerIPs();
}

public interface IConfigurationService
{
    string GetSetting(string key, string defaultValue = "");
    void SetSetting(string key, string value);
    void SaveSettings();
    List<VmsServerInfo> GetSavedServers();
    void SaveServer(VmsServerInfo server);
    void RemoveServer(string host, int port);
}
