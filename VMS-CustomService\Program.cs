using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.EventLog;
using VMSCustomService;

namespace VMSCustomService;

/// <summary>
/// Main entry point for VMS Production Windows Service
/// </summary>
public class Program
{
    /// <summary>
    /// Main entry point
    /// </summary>
    /// <param name="args">Command line arguments</param>
    public static void Main(string[] args)
    {
        CreateHostBuilder(args).Build().Run();
    }

    /// <summary>
    /// Create and configure the host builder
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Configured host builder</returns>
    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseWindowsService(options =>
            {
                options.ServiceName = "VMS-Production-Service";
            })
            .ConfigureServices((hostContext, services) =>
            {
                // Configure VMS settings
                services.Configure<VMSConfiguration>(
                    hostContext.Configuration.GetSection("VMS"));

                // Add the worker service
                services.AddHostedService<VMSWorkerService>();

                // Add HTTP client
                services.AddHttpClient();
            })
            .ConfigureLogging((hostContext, logging) =>
            {
                // Clear default providers
                logging.ClearProviders();

                // Add console logging for development
                logging.AddConsole();

                // Add Windows Event Log for production
                logging.AddEventLog(new EventLogSettings
                {
                    SourceName = "VMS-Production-Service",
                    LogName = "Application"
                });

                // Set logging levels from configuration
                logging.AddConfiguration(hostContext.Configuration.GetSection("Logging"));
            });
}
