@echo off
title VMS Client Launcher
echo.
echo ========================================
echo    VMS Client - .NET 8 LTS
echo    Auto-Discovery Windows Client
echo ========================================
echo.
echo Starting VMS Client...
echo.

REM Check if WebView2 is installed
echo Checking WebView2 Runtime...
reg query "HKLM\SOFTWARE\WOW6432Node\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo WARNING: Microsoft Edge WebView2 Runtime not found!
    echo The application may not work properly without it.
    echo.
    echo Please download and install WebView2 Runtime from:
    echo https://developer.microsoft.com/en-us/microsoft-edge/webview2/
    echo.
    pause
)

REM Launch the VMS Client
echo Launching VMS Client...
start "" "VMS-Client-NET8.exe"

REM Wait a moment then close launcher
timeout /t 3 /nobreak >nul
echo.
echo VMS Client launched successfully!
echo This window will close automatically...
timeout /t 2 /nobreak >nul
