import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'sonner';

// Components
import LoginPage from './components/LoginPage';
import Dashboard from './components/Dashboard';
import ConnectionStatus from './components/ConnectionStatus';

// Services
import { apiClient } from './services/ApiClient';
import { webSocketService } from './services/WebSocketService';

// Types
interface User {
  id: string;
  name: string;
  department: string;
  role: string;
}

interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
}

function App() {
  const [state, setState] = useState<AppState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    connectionStatus: 'connecting'
  });

  useEffect(() => {
    initializeApp();
    setupEventListeners();
    
    return () => {
      cleanupEventListeners();
    };
  }, []);

  /**
   * Initialize the application
   */
  const initializeApp = async () => {
    try {
      // Check if user is already authenticated
      await checkAuthStatus();
      
      // Monitor connection status
      monitorConnectionStatus();
      
    } catch (error) {
      console.error('App initialization error:', error);
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Check if user is already authenticated
   */
  const checkAuthStatus = async () => {
    try {
      const response = await apiClient.get('/auth/me');
      
      if (response.status === 200 && response.data) {
        setState(prev => ({
          ...prev,
          user: response.data,
          isAuthenticated: true
        }));
      }
    } catch (error) {
      // User not authenticated, which is fine
      console.log('User not authenticated');
    }
  };

  /**
   * Monitor connection status
   */
  const monitorConnectionStatus = () => {
    const checkConnection = async () => {
      try {
        const isConnected = await apiClient.testConnection();
        const wsStatus = webSocketService.getConnectionStatus();
        
        setState(prev => ({
          ...prev,
          connectionStatus: isConnected ? 'connected' : 'disconnected'
        }));
      } catch (error) {
        setState(prev => ({
          ...prev,
          connectionStatus: 'disconnected'
        }));
      }
    };

    // Check immediately
    checkConnection();
    
    // Check every 30 seconds
    const interval = setInterval(checkConnection, 30000);
    
    return () => clearInterval(interval);
  };

  /**
   * Setup event listeners
   */
  const setupEventListeners = () => {
    // Listen for authentication errors
    window.addEventListener('auth-error', handleAuthError);
    
    // Listen for WebSocket events
    webSocketService.on('connect', handleWebSocketConnect);
    webSocketService.on('disconnect', handleWebSocketDisconnect);
  };

  /**
   * Cleanup event listeners
   */
  const cleanupEventListeners = () => {
    window.removeEventListener('auth-error', handleAuthError);
    webSocketService.off('connect', handleWebSocketConnect);
    webSocketService.off('disconnect', handleWebSocketDisconnect);
  };

  /**
   * Handle authentication errors
   */
  const handleAuthError = () => {
    setState(prev => ({
      ...prev,
      user: null,
      isAuthenticated: false
    }));
  };

  /**
   * Handle WebSocket connection
   */
  const handleWebSocketConnect = () => {
    console.log('WebSocket connected');
  };

  /**
   * Handle WebSocket disconnection
   */
  const handleWebSocketDisconnect = () => {
    console.log('WebSocket disconnected');
  };

  /**
   * Handle successful login
   */
  const handleLogin = (user: User, sessionId: string) => {
    // Set session ID for API client
    apiClient.setSessionId(sessionId);
    
    // Update state
    setState(prev => ({
      ...prev,
      user,
      isAuthenticated: true
    }));

    // Connect WebSocket if not already connected
    webSocketService.connect().catch(error => {
      console.warn('WebSocket connection failed after login:', error);
    });
  };

  /**
   * Handle logout
   */
  const handleLogout = async () => {
    try {
      // Call logout API
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear session
      apiClient.setSessionId(null);
      
      // Disconnect WebSocket
      webSocketService.disconnect();
      
      // Update state
      setState(prev => ({
        ...prev,
        user: null,
        isAuthenticated: false
      }));
    }
  };

  // Show loading screen while initializing
  if (state.isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <div className="text-lg font-medium">Initializing VMS Client...</div>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <div className="min-h-screen bg-background">
        {/* Connection Status Indicator */}
        <ConnectionStatus status={state.connectionStatus} />
        
        {/* Toast Notifications */}
        <Toaster 
          position="top-right" 
          richColors 
          closeButton 
          duration={4000}
        />
        
        {/* Main Application Routes */}
        <Routes>
          <Route 
            path="/login" 
            element={
              state.isAuthenticated ? 
                <Navigate to="/dashboard" replace /> : 
                <LoginPage onLogin={handleLogin} />
            } 
          />
          
          <Route 
            path="/dashboard/*" 
            element={
              state.isAuthenticated ? 
                <Dashboard 
                  user={state.user!} 
                  onLogout={handleLogout}
                  connectionStatus={state.connectionStatus}
                /> : 
                <Navigate to="/login" replace />
            } 
          />
          
          <Route 
            path="/" 
            element={
              <Navigate 
                to={state.isAuthenticated ? "/dashboard" : "/login"} 
                replace 
              />
            } 
          />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
