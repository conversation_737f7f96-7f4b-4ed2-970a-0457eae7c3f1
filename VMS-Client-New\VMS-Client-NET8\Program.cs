using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Windows.Forms;
using VMS.Client.Services;
using VMS.Client.Forms;

namespace VMS.Client;

internal static class Program
{
    /// <summary>
    /// The main entry point for the VMS Client application.
    /// </summary>
    [STAThread]
    static async Task Main()
    {
        // Configure Windows Forms
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);
        Application.SetHighDpiMode(HighDpiMode.SystemAware);

        // Build host with dependency injection
        var host = CreateHostBuilder().Build();

        try
        {
            // Start the host
            await host.StartAsync();

            // Get the main form from DI container
            var mainForm = host.Services.GetRequiredService<MainForm>();

            // Run the Windows Forms application
            Application.Run(mainForm);
        }
        catch (Exception ex)
        {
            MessageBox.Show(
                $"Application startup failed:\n\n{ex.Message}",
                "VMS Client Error",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error
            );
        }
        finally
        {
            await host.StopAsync();
            host.Dispose();
        }
    }

    static IHostBuilder CreateHostBuilder() =>
        Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Register services
                services.AddHttpClient();
                services.AddSingleton<IServerDiscoveryService, ServerDiscoveryService>();
                services.AddSingleton<IVmsApiService, VmsApiService>();
                services.AddSingleton<INetworkService, NetworkService>();
                services.AddSingleton<IConfigurationService, ConfigurationService>();
                
                // Register forms
                services.AddTransient<MainForm>();
                services.AddTransient<ServerDiscoveryForm>();
                services.AddTransient<SettingsForm>();
                
                // Configure logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.AddDebug();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            });
}
