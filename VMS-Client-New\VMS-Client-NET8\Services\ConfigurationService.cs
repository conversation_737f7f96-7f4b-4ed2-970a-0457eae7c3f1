using Microsoft.Extensions.Logging;
using System.Text.Json;
using VMS.Client.Models;

namespace VMS.Client.Services;

public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configPath;
    private readonly Dictionary<string, string> _settings = new();
    private readonly List<VmsServerInfo> _savedServers = new();

    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger;
        _configPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "VMS-Client",
            "config.json"
        );
        
        LoadSettings();
    }

    public string GetSetting(string key, string defaultValue = "")
    {
        return _settings.TryGetValue(key, out var value) ? value : defaultValue;
    }

    public void SetSetting(string key, string value)
    {
        _settings[key] = value;
    }

    public void SaveSettings()
    {
        try
        {
            var configDir = Path.GetDirectoryName(_configPath);
            if (!string.IsNullOrEmpty(configDir) && !Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            var config = new
            {
                Settings = _settings,
                SavedServers = _savedServers.Select(s => new
                {
                    s.ServiceName,
                    s.Host,
                    s.Port,
                    s.Version,
                    s.LastSeen
                }).ToList(),
                LastUpdated = DateTime.Now
            };

            var json = JsonSerializer.Serialize(config, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
            
            File.WriteAllText(_configPath, json);
            _logger.LogDebug("Settings saved to {ConfigPath}", _configPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save settings");
        }
    }

    public List<VmsServerInfo> GetSavedServers()
    {
        return _savedServers.ToList();
    }

    public void SaveServer(VmsServerInfo server)
    {
        var existing = _savedServers.FirstOrDefault(s => s.Host == server.Host && s.Port == server.Port);
        if (existing != null)
        {
            _savedServers.Remove(existing);
        }
        
        _savedServers.Add(server);
        SaveSettings();
    }

    public void RemoveServer(string host, int port)
    {
        var server = _savedServers.FirstOrDefault(s => s.Host == host && s.Port == port);
        if (server != null)
        {
            _savedServers.Remove(server);
            SaveSettings();
        }
    }

    private void LoadSettings()
    {
        try
        {
            if (File.Exists(_configPath))
            {
                var json = File.ReadAllText(_configPath);
                var config = JsonSerializer.Deserialize<JsonElement>(json);

                if (config.TryGetProperty("Settings", out var settingsElement))
                {
                    foreach (var setting in settingsElement.EnumerateObject())
                    {
                        _settings[setting.Name] = setting.Value.GetString() ?? "";
                    }
                }

                if (config.TryGetProperty("SavedServers", out var serversElement))
                {
                    foreach (var serverElement in serversElement.EnumerateArray())
                    {
                        var server = new VmsServerInfo
                        {
                            ServiceName = serverElement.GetProperty("ServiceName").GetString() ?? "",
                            Host = serverElement.GetProperty("Host").GetString() ?? "",
                            Port = serverElement.GetProperty("Port").GetInt32(),
                            Version = serverElement.GetProperty("Version").GetString() ?? "",
                        };

                        if (serverElement.TryGetProperty("LastSeen", out var lastSeenElement))
                        {
                            if (DateTime.TryParse(lastSeenElement.GetString(), out var lastSeen))
                            {
                                server.LastSeen = lastSeen;
                            }
                        }

                        _savedServers.Add(server);
                    }
                }

                _logger.LogDebug("Settings loaded from {ConfigPath}", _configPath);
            }
            else
            {
                // Set default settings
                SetDefaultSettings();
                SaveSettings();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load settings, using defaults");
            SetDefaultSettings();
        }
    }

    private void SetDefaultSettings()
    {
        _settings["AutoDiscovery"] = "true";
        _settings["DefaultTimeout"] = "5000";
        _settings["PreferredServer"] = "";
        _settings["LastConnectedServer"] = "";
        _settings["WindowWidth"] = "1024";
        _settings["WindowHeight"] = "768";
        _settings["StartMinimized"] = "false";
    }
}
