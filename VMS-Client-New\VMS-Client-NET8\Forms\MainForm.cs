using Microsoft.Extensions.Logging;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using System.ComponentModel;
using VMS.Client.Models;
using VMS.Client.Services;

namespace VMS.Client.Forms;

public partial class MainForm : Form
{
    private readonly ILogger<MainForm> _logger;
    private readonly IServerDiscoveryService _discoveryService;
    private readonly IConfigurationService _configService;
    private readonly IVmsApiService _apiService;
    
    private WebView2 _webView;
    private StatusStrip _statusStrip;
    private ToolStripStatusLabel _statusLabel;
    private ToolStripProgressBar _progressBar;
    private MenuStrip _menuStrip;
    private ToolStrip _toolStrip;
    
    private VmsServerInfo? _currentServer;
    private bool _isConnected;

    public MainForm(
        ILogger<MainForm> logger,
        IServerDiscoveryService discoveryService,
        IConfigurationService configService,
        IVmsApiService apiService)
    {
        _logger = logger;
        _discoveryService = discoveryService;
        _configService = configService;
        _apiService = apiService;
        
        InitializeComponent();
        SetupEventHandlers();
        
        // Start auto-discovery on load
        _ = Task.Run(StartAutoDiscoveryAsync);
    }

    private void InitializeComponent()
    {
        SuspendLayout();
        
        // Form properties
        Text = "VMS Client - Connecting...";
        Size = new Size(
            int.Parse(_configService.GetSetting("WindowWidth", "1200")),
            int.Parse(_configService.GetSetting("WindowHeight", "800"))
        );
        StartPosition = FormStartPosition.CenterScreen;
        Icon = LoadIcon();
        
        // Create menu strip
        CreateMenuStrip();
        
        // Create toolbar
        CreateToolStrip();
        
        // Create WebView2
        CreateWebView();
        
        // Create status strip
        CreateStatusStrip();
        
        ResumeLayout(false);
        PerformLayout();
    }

    private void CreateMenuStrip()
    {
        _menuStrip = new MenuStrip();
        
        // File menu
        var fileMenu = new ToolStripMenuItem("&File");
        fileMenu.DropDownItems.Add("&Connect to Server...", null, OnConnectClick);
        fileMenu.DropDownItems.Add("&Disconnect", null, OnDisconnectClick);
        fileMenu.DropDownItems.Add(new ToolStripSeparator());
        fileMenu.DropDownItems.Add("&Settings...", null, OnSettingsClick);
        fileMenu.DropDownItems.Add(new ToolStripSeparator());
        fileMenu.DropDownItems.Add("E&xit", null, OnExitClick);
        
        // Tools menu
        var toolsMenu = new ToolStripMenuItem("&Tools");
        toolsMenu.DropDownItems.Add("&Discover Servers", null, OnDiscoverClick);
        toolsMenu.DropDownItems.Add("&Refresh Connection", null, OnRefreshClick);
        toolsMenu.DropDownItems.Add(new ToolStripSeparator());
        toolsMenu.DropDownItems.Add("&Clear Cache", null, OnClearCacheClick);
        
        // Help menu
        var helpMenu = new ToolStripMenuItem("&Help");
        helpMenu.DropDownItems.Add("&About VMS Client", null, OnAboutClick);
        
        _menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, toolsMenu, helpMenu });
        MainMenuStrip = _menuStrip;
        Controls.Add(_menuStrip);
    }

    private void CreateToolStrip()
    {
        _toolStrip = new ToolStrip();
        
        var connectButton = new ToolStripButton("Connect", null, OnConnectClick)
        {
            DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
            ToolTipText = "Connect to VMS Server"
        };
        
        var disconnectButton = new ToolStripButton("Disconnect", null, OnDisconnectClick)
        {
            DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
            ToolTipText = "Disconnect from VMS Server"
        };
        
        var discoverButton = new ToolStripButton("Discover", null, OnDiscoverClick)
        {
            DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
            ToolTipText = "Discover VMS Servers"
        };
        
        var refreshButton = new ToolStripButton("Refresh", null, OnRefreshClick)
        {
            DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
            ToolTipText = "Refresh Connection"
        };
        
        _toolStrip.Items.AddRange(new ToolStripItem[] 
        { 
            connectButton, 
            disconnectButton, 
            new ToolStripSeparator(),
            discoverButton,
            refreshButton
        });
        
        Controls.Add(_toolStrip);
    }

    private void CreateWebView()
    {
        _webView = new WebView2
        {
            Dock = DockStyle.Fill
        };
        
        Controls.Add(_webView);
    }

    private void CreateStatusStrip()
    {
        _statusStrip = new StatusStrip();
        
        _statusLabel = new ToolStripStatusLabel("Ready")
        {
            Spring = true,
            TextAlign = ContentAlignment.MiddleLeft
        };
        
        _progressBar = new ToolStripProgressBar
        {
            Visible = false
        };
        
        var connectionStatus = new ToolStripStatusLabel("Disconnected")
        {
            BorderSides = ToolStripStatusLabelBorderSides.Left
        };
        
        _statusStrip.Items.AddRange(new ToolStripItem[] 
        { 
            _statusLabel, 
            _progressBar, 
            connectionStatus 
        });
        
        Controls.Add(_statusStrip);
    }

    private void SetupEventHandlers()
    {
        _discoveryService.StatusChanged += OnDiscoveryStatusChanged;
        _discoveryService.ServersDiscovered += OnServersDiscovered;
        
        FormClosing += OnFormClosing;
        Resize += OnFormResize;
    }

    private async Task StartAutoDiscoveryAsync()
    {
        try
        {
            UpdateStatus("Starting auto-discovery...");
            ShowProgress(true);
            
            var result = await _discoveryService.DiscoverServersAsync();
            
            if (result.Success && result.BestServer != null)
            {
                await ConnectToServerAsync(result.BestServer);
            }
            else
            {
                UpdateStatus("No VMS servers found. Please check network connection.");
                ShowConnectDialog();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Auto-discovery failed");
            UpdateStatus("Auto-discovery failed. Please connect manually.");
            ShowConnectDialog();
        }
        finally
        {
            ShowProgress(false);
        }
    }

    private async Task ConnectToServerAsync(VmsServerInfo server)
    {
        try
        {
            UpdateStatus($"Connecting to {server.Url}...");
            ShowProgress(true);
            
            var isHealthy = await _apiService.TestConnectionAsync(server.Url);
            
            if (isHealthy)
            {
                _currentServer = server;
                _isConnected = true;
                
                await _webView.EnsureCoreWebView2Async();
                _webView.CoreWebView2.Navigate(server.Url);
                
                Text = $"VMS Client - Connected to {server.Host}:{server.Port}";
                UpdateStatus($"Connected to {server.Url}");
                
                _configService.SetSetting("LastConnectedServer", server.Url);
                _configService.SaveServer(server);
                _configService.SaveSettings();
                
                _logger.LogInformation("Successfully connected to VMS server: {Url}", server.Url);
            }
            else
            {
                UpdateStatus($"Failed to connect to {server.Url}");
                ShowConnectDialog();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Connection failed");
            UpdateStatus($"Connection failed: {ex.Message}");
            ShowConnectDialog();
        }
        finally
        {
            ShowProgress(false);
        }
    }

    private void ShowConnectDialog()
    {
        if (InvokeRequired)
        {
            Invoke(ShowConnectDialog);
            return;
        }
        
        using var dialog = new ServerDiscoveryForm(_discoveryService, _configService);
        if (dialog.ShowDialog(this) == DialogResult.OK && dialog.SelectedServer != null)
        {
            _ = Task.Run(() => ConnectToServerAsync(dialog.SelectedServer));
        }
    }

    private void UpdateStatus(string message)
    {
        if (InvokeRequired)
        {
            Invoke(() => UpdateStatus(message));
            return;
        }
        
        _statusLabel.Text = message;
        _logger.LogDebug("Status: {Message}", message);
    }

    private void ShowProgress(bool show)
    {
        if (InvokeRequired)
        {
            Invoke(() => ShowProgress(show));
            return;
        }
        
        _progressBar.Visible = show;
        if (show)
        {
            _progressBar.Style = ProgressBarStyle.Marquee;
        }
    }

    private Icon? LoadIcon()
    {
        try
        {
            var iconPath = Path.Combine(Application.StartupPath, "vms-icon.ico");
            if (File.Exists(iconPath))
            {
                return new Icon(iconPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to load icon");
        }
        
        return null;
    }

    // Event handlers
    private void OnDiscoveryStatusChanged(object? sender, string status)
    {
        UpdateStatus(status);
    }

    private void OnServersDiscovered(object? sender, DiscoveryResult result)
    {
        _logger.LogInformation("Discovered {Count} servers", result.Servers.Count);
    }

    private void OnConnectClick(object? sender, EventArgs e)
    {
        ShowConnectDialog();
    }

    private void OnDisconnectClick(object? sender, EventArgs e)
    {
        _isConnected = false;
        _currentServer = null;
        Text = "VMS Client - Disconnected";
        UpdateStatus("Disconnected");
        
        if (_webView.CoreWebView2 != null)
        {
            _webView.CoreWebView2.Navigate("about:blank");
        }
    }

    private async void OnDiscoverClick(object? sender, EventArgs e)
    {
        try
        {
            ShowProgress(true);
            await _discoveryService.DiscoverServersAsync();
        }
        finally
        {
            ShowProgress(false);
        }
    }

    private async void OnRefreshClick(object? sender, EventArgs e)
    {
        if (_currentServer != null)
        {
            await ConnectToServerAsync(_currentServer);
        }
        else
        {
            await StartAutoDiscoveryAsync();
        }
    }

    private void OnClearCacheClick(object? sender, EventArgs e)
    {
        _discoveryService.ClearCache();
        UpdateStatus("Discovery cache cleared");
    }

    private void OnSettingsClick(object? sender, EventArgs e)
    {
        using var dialog = new SettingsForm(_configService);
        dialog.ShowDialog(this);
    }

    private void OnAboutClick(object? sender, EventArgs e)
    {
        MessageBox.Show(
            "VMS Client v1.0.0\n\n" +
            "A Windows client for the VMS Production System\n" +
            "with automatic server discovery capabilities.\n\n" +
            "Built with .NET 8 LTS for Windows 10/11",
            "About VMS Client",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information
        );
    }

    private void OnExitClick(object? sender, EventArgs e)
    {
        Close();
    }

    private void OnFormClosing(object? sender, FormClosingEventArgs e)
    {
        // Save window state
        if (WindowState == FormWindowState.Normal)
        {
            _configService.SetSetting("WindowWidth", Width.ToString());
            _configService.SetSetting("WindowHeight", Height.ToString());
        }
        
        _configService.SaveSettings();
    }

    private void OnFormResize(object? sender, EventArgs e)
    {
        if (WindowState == FormWindowState.Minimized && 
            _configService.GetSetting("MinimizeToTray", "false") == "true")
        {
            Hide();
            ShowInTaskbar = false;
        }
    }
}
