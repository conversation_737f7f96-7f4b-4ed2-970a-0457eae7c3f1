using Microsoft.Extensions.Logging;
using System.Text.Json;
using VMS.Client.Models;

namespace VMS.Client.Services;

public class VmsApiService : IVmsApiService
{
    private readonly ILogger<VmsApiService> _logger;
    private readonly IHttpClientFactory _httpClientFactory;

    public VmsApiService(ILogger<VmsApiService> logger, IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<bool> TestConnectionAsync(string baseUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(5);
            
            var response = await httpClient.GetAsync($"{baseUrl}/api/health", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Connection test failed for {BaseUrl}", baseUrl);
            return false;
        }
    }

    public async Task<ServerHealthInfo?> GetHealthInfoAsync(string baseUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(5);
            
            var response = await httpClient.GetAsync($"{baseUrl}/api/health", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var healthInfo = JsonSerializer.Deserialize<ServerHealthInfo>(content, 
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                
                return healthInfo;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to get health info from {BaseUrl}", baseUrl);
        }
        
        return null;
    }
}
