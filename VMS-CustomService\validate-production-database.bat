@echo off
echo ========================================
echo VMS Production Database Validator
echo ========================================

echo [1/5] Checking VMS Service Status...
sc query VMS-Production-Service | findstr "RUNNING" >nul
if %errorlevel% neq 0 (
    echo ERROR: VMS Service must be running for database validation
    pause
    exit /b 1
)
echo ✓ VMS Service is running

echo.
echo [2/5] Testing database connectivity...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/health' -UseBasicParsing -TimeoutSec 10; $health = $response.Content | ConvertFrom-Json; if ($health.status -eq 'healthy') { Write-Host '✓ Database connectivity: HEALTHY'; Write-Host '✓ Environment:' $health.environment; Write-Host '✓ Uptime:' $health.uptime; exit 0 } else { Write-Host 'ERROR: Database health check failed'; exit 1 } } catch { Write-Host 'ERROR: Cannot connect to VMS health endpoint'; exit 1 }"
if %errorlevel% neq 0 (
    echo Database validation failed!
    pause
    exit /b 1
)

echo.
echo [3/5] Testing API endpoints...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/api' -UseBasicParsing -TimeoutSec 5; $api = $response.Content | ConvertFrom-Json; if ($response.StatusCode -eq 200 -and $api.status -eq 'active') { Write-Host '✓ API system: ACTIVE'; Write-Host '✓ Version:' $api.version; exit 0 } else { Write-Host 'ERROR: API system failed'; exit 1 } } catch { Write-Host 'ERROR: API endpoint failed'; exit 1 }"
if %errorlevel% neq 0 (
    echo API validation failed!
    pause
    exit /b 1
)

echo.
echo [4/5] Testing authentication security...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8080/api/users/online' -UseBasicParsing -TimeoutSec 5 } catch { if ($_.Exception.Message -match 'Authentication required' -or $_.Exception.Message -match 'Unauthorized') { Write-Host '✓ Authentication security: ENFORCED'; exit 0 } else { Write-Host 'ERROR: Unexpected authentication error'; exit 1 } }"
if %errorlevel% neq 0 (
    echo Authentication security validation failed!
    pause
    exit /b 1
)

echo.
echo [5/5] Checking error logs for critical issues...
powershell -Command "if (Test-Path 'C:\Program Files\DEPLOYMENT PACKAGE\Server\logs\error.log') { $recentErrors = Get-Content 'C:\Program Files\DEPLOYMENT PACKAGE\Server\logs\error.log' -Tail 10 | Where-Object { $_ -match 'error' -and $_ -match (Get-Date).ToString('yyyy-MM-dd') }; if ($recentErrors) { Write-Host 'WARNING: Recent errors detected:'; $recentErrors | ForEach-Object { Write-Host '  ' $_ } } else { Write-Host '✓ No critical errors in recent logs' } } else { Write-Host 'WARNING: Error log file not found' }"

echo.
echo ========================================
echo ✅ VMS PRODUCTION DATABASE VALIDATION COMPLETE
echo ========================================
echo.
echo Summary:
echo ✓ Service Status: RUNNING
echo ✓ Database Connectivity: HEALTHY
echo ✓ API System: ACTIVE
echo ✓ Authentication Security: ENFORCED
echo ✓ Error Monitoring: ACTIVE
echo.
echo 🎉 VMS Production System is FULLY OPERATIONAL!
echo.
pause
