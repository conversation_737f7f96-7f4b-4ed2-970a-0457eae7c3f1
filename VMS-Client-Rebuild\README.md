# VMS Client Application

A .NET 8 Windows Forms application that replicates the functionality of the original VMS client with automatic server discovery.

## Features

- **Exact Interface Match**: Replicates the original VMS client interface design
- **Auto-Discovery**: Automatically finds VMS server on the network via UDP broadcasts
- **Browser Integration**: Opens VMS interface in external browser (preferably Chrome)
- **Self-Contained**: Single executable with no installation required
- **Windows 10/11 Compatible**: Works on all modern Windows systems with .NET 8+

## Building the Application

### Prerequisites
- .NET 8.0 SDK or later
- Windows 10/11 development machine

### Build Steps
1. Open Command Prompt in the `VMS-Client-Rebuild` folder
2. Run the build script:
   ```cmd
   build.bat
   ```
3. The executable will be created at: `publish\win-x64\VMS-Client.exe`

### Manual Build (Alternative)
```cmd
dotnet restore
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "publish\win-x64" -p:PublishSingleFile=true
```

## Deployment

1. Copy `VMS-Client.exe` from the `publish\win-x64` folder
2. Place it on any Windows 10/11 PC
3. Run the executable - no installation required

## How It Works

### Auto-Discovery Process
1. Client listens for UDP broadcasts on port 45454
2. VMS server broadcasts service announcements every 5 seconds
3. Client automatically connects when server is found
4. Falls back to manual discovery requests if no broadcasts received

### Interface Flow
1. **Discovery Screen**: Shows VMS logo and "Connect to VMS" button
2. **Auto-Connect**: Searches for server automatically
3. **Browser Launch**: Opens full VMS web application in external browser (Chrome preferred)

### Configuration
- Settings stored in: `%APPDATA%\VMS-Client\config.json`
- Automatically saves last connected server
- Supports manual server configuration if needed

## Firewall Requirements

If auto-discovery doesn't work on client PCs, run this command as Administrator:

```cmd
netsh advfirewall firewall add rule name="VMS Client Discovery" dir=in action=allow protocol=UDP localport=45454
```

## Troubleshooting

### Client Can't Find Server
1. Check Windows Firewall (run firewall fix command above)
2. Verify VMS server is running and broadcasting
3. Test with: `node test-vms-discovery.js` (if Node.js available)

### Connection Issues
1. Verify server is accessible at `http://[server-ip]:8080/health`
2. Check network connectivity between client and server PCs
3. Ensure no proxy or network restrictions

## File Structure

```
VMS-Client-Rebuild/
├── VMS-Client.csproj          # Project file
├── Program.cs                 # Application entry point
├── MainForm.cs                # Main application window
├── Services/
│   ├── ConfigurationService.cs   # Settings management
│   └── ServerDiscoveryService.cs # Auto-discovery logic
├── build.bat                  # Build script
└── README.md                  # This file
```

## Version Information

- **Application**: VMS Main Client v4.0 - Network Discovery Edition
- **Framework**: .NET 8.0
- **UI**: Windows Forms with WebView2
- **Target**: Windows 10/11 x64
