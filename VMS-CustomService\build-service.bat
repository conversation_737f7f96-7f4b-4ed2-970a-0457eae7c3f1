@echo off
echo ========================================
echo VMS Custom Windows Service Builder
echo ========================================

set SERVICE_NAME=VMS-Production-Service
set BUILD_CONFIG=Release
set OUTPUT_DIR=bin\%BUILD_CONFIG%\net8.0-windows\win-x64\publish

echo [1/4] Cleaning previous builds...
if exist "%OUTPUT_DIR%" (
    rmdir /s /q "%OUTPUT_DIR%"
)

echo [2/4] Restoring NuGet packages...
dotnet restore
if %errorLevel% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

echo [3/4] Building and publishing service...
dotnet publish -c %BUILD_CONFIG% -r win-x64 --self-contained true -p:PublishSingleFile=true
if %errorLevel% neq 0 (
    echo ERROR: Failed to build service
    pause
    exit /b 1
)

echo [4/4] Build completed successfully!
echo.
echo Service executable: %OUTPUT_DIR%\VMS-CustomService.exe
echo.
echo Next steps:
echo 1. Run 'deploy-custom-service.bat' as Administrator to install the service
echo 2. Use 'manage-custom-service.bat' to control the service
echo.
pause
