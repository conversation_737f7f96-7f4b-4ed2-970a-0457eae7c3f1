# VMS Client - .NET 8 LTS

A Windows desktop client for the VMS Production System with automatic server discovery capabilities.

## Features

### 🔍 **Auto-Discovery**
- **Network Scanning**: Automatically discovers VMS servers on the local network
- **Multi-Method Discovery**: Uses HTTP health checks, network scanning, and known server lists
- **Smart Selection**: Automatically selects the best available server based on ping and availability
- **Cached Results**: Remembers discovered servers for faster subsequent connections

### 🌐 **Connection Management**
- **WebView2 Integration**: Full VMS web interface embedded in native Windows application
- **Connection Testing**: Validates server health before connecting
- **Manual Connection**: Option to manually specify server address and port
- **Saved Servers**: Remembers previously connected servers

### ⚙️ **Configuration**
- **Persistent Settings**: Saves user preferences and server history
- **Timeout Configuration**: Adjustable connection timeouts
- **UI Preferences**: Window size, minimize to tray, startup options
- **Preferred Servers**: Set preferred server addresses

### 🖥️ **Windows Integration**
- **Native Windows Forms**: Familiar Windows UI experience
- **System Tray Support**: Minimize to system tray (optional)
- **High DPI Aware**: Supports high-resolution displays
- **Windows 10/11 Compatible**: Optimized for modern Windows versions

## System Requirements

- **Operating System**: Windows 10 version 1903 or later, Windows 11
- **.NET Runtime**: .NET 8 LTS (included in self-contained build)
- **WebView2**: Microsoft Edge WebView2 Runtime (auto-installed if missing)
- **Memory**: 100MB RAM minimum
- **Disk Space**: 150MB for installation

## Installation

### Option 1: Self-Contained Executable (Recommended)
1. Download the latest release from the `publish\win-x64` folder
2. Run `VMS-Client-NET8.exe` directly - no installation required
3. The application will auto-discover VMS servers on first run

### Option 2: Build from Source
1. Install .NET 8 SDK
2. Clone or download the source code
3. Run `build.bat` to build the application
4. Executable will be created in `publish\win-x64\VMS-Client-NET8.exe`

## Usage

### First Run
1. Launch `VMS-Client-NET8.exe`
2. The application will automatically scan for VMS servers
3. If servers are found, it will connect to the best available server
4. If no servers are found, use the "Connect to Server" dialog

### Manual Connection
1. Click "File" → "Connect to Server..." or use the toolbar button
2. Click "Discover Servers" to scan the network
3. Or manually enter server details in the "Manual Connection" section
4. Select a server and click "Connect"

### Settings
- Access via "File" → "Settings..."
- Configure auto-discovery, timeouts, and UI preferences
- Set preferred server addresses

## Network Discovery

The client uses multiple discovery methods:

1. **Localhost Check**: Tests common ports on localhost first
2. **Known Servers**: Tests configured production server IPs
3. **Network Scanning**: Scans local network ranges for VMS servers
4. **Port Scanning**: Tests common ports on discovered IPs

### Default Server Addresses
- `************:8080` (Primary production server)
- `************:8080` (Secondary server)
- `localhost:8080` (Local development)

## Configuration Files

### Application Settings
- **Location**: `%APPDATA%\VMS-Client\config.json`
- **Contains**: User preferences, saved servers, window settings

### Application Configuration
- **File**: `appsettings.json`
- **Contains**: Default settings, discovery configuration, logging settings

## Troubleshooting

### Connection Issues
1. **Check Network**: Ensure you're on the same network as the VMS server
2. **Firewall**: Verify Windows Firewall allows the application
3. **Server Status**: Confirm the VMS server is running and accessible
4. **Manual Connection**: Try connecting manually with known server IP

### Discovery Issues
1. **Network Permissions**: Run as administrator if network scanning fails
2. **Antivirus**: Add application to antivirus exclusions
3. **VPN/Proxy**: Disable VPN or proxy that might block network discovery

### WebView2 Issues
1. **Missing Runtime**: Download Microsoft Edge WebView2 Runtime
2. **Outdated Runtime**: Update to latest WebView2 version
3. **Permissions**: Ensure application has internet access permissions

## Development

### Building
```bash
# Restore packages
dotnet restore

# Build debug version
dotnet build

# Build release version
dotnet build --configuration Release

# Publish self-contained
dotnet publish --configuration Release --runtime win-x64 --self-contained true
```

### Dependencies
- Microsoft.Extensions.Hosting
- Microsoft.Extensions.Logging
- Microsoft.Web.WebView2
- System.Net.NetworkInformation
- System.Management

## Support

For issues or questions:
1. Check the application logs in `%APPDATA%\VMS-Client\logs`
2. Verify network connectivity to VMS server
3. Try manual connection with known server address
4. Contact VMS system administrator

## Version History

### v1.0.0
- Initial release with auto-discovery
- WebView2 integration
- Windows Forms UI
- Configuration management
- Network scanning capabilities
