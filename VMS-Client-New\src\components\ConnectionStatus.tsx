import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, Loader2 } from 'lucide-react';
import { apiClient } from '../services/ApiClient';

interface ConnectionStatusProps {
  status: 'connected' | 'connecting' | 'disconnected';
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ status }) => {
  const [serverInfo, setServerInfo] = useState<any>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    if (status === 'connected') {
      fetchServerInfo();
    }
  }, [status]);

  const fetchServerInfo = async () => {
    try {
      const currentServer = apiClient.getCurrentServer();
      const health = await apiClient.getServerHealth();
      
      setServerInfo({
        server: currentServer,
        health
      });
    } catch (error) {
      console.error('Failed to fetch server info:', error);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return <Wifi className="w-4 h-4" />;
      case 'connecting':
        return <Loader2 className="w-4 h-4 animate-spin" />;
      case 'disconnected':
        return <WifiOff className="w-4 h-4" />;
      default:
        return <WifiOff className="w-4 h-4" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'disconnected':
        return 'Disconnected';
      default:
        return 'Unknown';
    }
  };

  const getStatusClass = () => {
    switch (status) {
      case 'connected':
        return 'connection-indicator connected';
      case 'connecting':
        return 'connection-indicator connecting';
      case 'disconnected':
        return 'connection-indicator disconnected';
      default:
        return 'connection-indicator disconnected';
    }
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  return (
    <>
      <div 
        className={getStatusClass()}
        onClick={() => setShowDetails(!showDetails)}
        style={{ cursor: 'pointer' }}
      >
        <div className="status-dot"></div>
        {getStatusIcon()}
        <span>{getStatusText()}</span>
        {serverInfo?.server && (
          <span className="text-xs opacity-75">
            ({serverInfo.server.host}:{serverInfo.server.port})
          </span>
        )}
      </div>

      {/* Detailed Status Modal */}
      {showDetails && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => setShowDetails(false)}
        >
          <div 
            className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Connection Status</h3>
              <button 
                onClick={() => setShowDetails(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              {/* Connection Status */}
              <div className="flex items-center gap-3">
                {getStatusIcon()}
                <div>
                  <div className="font-medium">{getStatusText()}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {status === 'connected' ? 'VMS server is reachable' : 
                     status === 'connecting' ? 'Attempting to connect...' : 
                     'Unable to reach VMS server'}
                  </div>
                </div>
              </div>

              {/* Server Information */}
              {serverInfo && (
                <>
                  <hr className="border-gray-200 dark:border-gray-700" />
                  
                  <div>
                    <h4 className="font-medium mb-2">Server Information</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">URL:</span>
                        <span className="font-mono">{serverInfo.server?.url}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Version:</span>
                        <span>{serverInfo.health?.version || 'Unknown'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Uptime:</span>
                        <span>
                          {serverInfo.health?.uptime ? 
                            formatUptime(serverInfo.health.uptime) : 
                            'Unknown'
                          }
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Environment:</span>
                        <span className="capitalize">
                          {serverInfo.health?.environment || 'Unknown'}
                        </span>
                      </div>
                      {serverInfo.server?.ping && (
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Ping:</span>
                          <span>{serverInfo.server.ping}ms</span>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Actions */}
              <hr className="border-gray-200 dark:border-gray-700" />
              
              <div className="flex gap-2">
                <button
                  onClick={async () => {
                    try {
                      await fetchServerInfo();
                    } catch (error) {
                      console.error('Refresh failed:', error);
                    }
                  }}
                  className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  Refresh
                </button>
                
                {status === 'disconnected' && (
                  <button
                    onClick={() => {
                      window.location.reload();
                    }}
                    className="flex-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
                  >
                    Reconnect
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ConnectionStatus;
