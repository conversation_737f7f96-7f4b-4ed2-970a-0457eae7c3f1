namespace VMSCustomService;

/// <summary>
/// Configuration class for VMS Production Service
/// Maps to the "VMS" section in appsettings.json
/// </summary>
public class VMSConfiguration
{
    /// <summary>
    /// Path to the VMS Server directory
    /// </summary>
    public string ServerPath { get; set; } = "C:\\VMS-PRODUCTION\\Server";

    /// <summary>
    /// Node.js executable name or path
    /// </summary>
    public string NodeExecutable { get; set; } = "node";

    /// <summary>
    /// VMS server script to execute
    /// </summary>
    public string ServerScript { get; set; } = "dist\\index.js";

    /// <summary>
    /// Port number for the VMS server
    /// </summary>
    public int Port { get; set; } = 8080;

    /// <summary>
    /// Health check interval in milliseconds
    /// </summary>
    public int HealthCheckInterval { get; set; } = 30000;

    /// <summary>
    /// Delay before restarting failed service in milliseconds
    /// </summary>
    public int RestartDelay { get; set; } = 5000;

    /// <summary>
    /// Maximum number of restart attempts before giving up
    /// </summary>
    public int MaxRestartAttempts { get; set; } = 3;

    /// <summary>
    /// URL for health check endpoint
    /// </summary>
    public string HealthCheckUrl { get; set; } = "http://localhost:8080/health";

    /// <summary>
    /// Process timeout in milliseconds
    /// </summary>
    public int ProcessTimeout { get; set; } = 30000;
}
