<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VMS - Voucher Management System</title>
    <meta name="description" content="Dynamic VMS Client with Automatic Server Discovery" />
    <style>
      /* Loading screen styles */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      .loading-subtext {
        font-size: 14px;
        opacity: 0.8;
        text-align: center;
        max-width: 400px;
      }
      
      .server-status {
        margin-top: 20px;
        padding: 10px 20px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        font-size: 12px;
        font-family: monospace;
      }
    </style>
  </head>
  <body>
    <div id="loading-screen" class="loading-screen">
      <div class="loading-spinner"></div>
      <div class="loading-text">VMS Client Starting...</div>
      <div class="loading-subtext">Discovering VMS server on the network</div>
      <div id="server-status" class="server-status">
        Initializing...
      </div>
    </div>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
