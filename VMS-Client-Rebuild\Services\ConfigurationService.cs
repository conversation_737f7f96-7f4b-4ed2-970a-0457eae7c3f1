using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;

namespace VMS_Client.Services
{
    public class ConfigurationService
    {
        private readonly string _configPath;
        private ClientConfiguration _config;

        public ConfigurationService()
        {
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "VMS-Client");
            Directory.CreateDirectory(appDataPath);
            _configPath = Path.Combine(appDataPath, "config.json");
            
            LoadConfiguration();
        }

        public ClientConfiguration Configuration => _config;

        private void LoadConfiguration()
        {
            try
            {
                if (File.Exists(_configPath))
                {
                    var json = File.ReadAllText(_configPath);
                    _config = JsonConvert.DeserializeObject<ClientConfiguration>(json) ?? CreateDefaultConfiguration();
                }
                else
                {
                    _config = CreateDefaultConfiguration();
                    SaveConfiguration();
                }
            }
            catch (Exception)
            {
                _config = CreateDefaultConfiguration();
            }
        }

        public void SaveConfiguration()
        {
            try
            {
                _config.LastUpdated = DateTime.UtcNow;
                var json = JsonConvert.SerializeObject(_config, Formatting.Indented);
                File.WriteAllText(_configPath, json);
            }
            catch (Exception)
            {
                // Ignore save errors
            }
        }

        public void UpdateLastConnectedServer(string serverUrl)
        {
            _config.Settings.LastConnectedServer = serverUrl;
            _config.Settings.PreferredServer = serverUrl;
            
            // Add to saved servers if not already present
            var existingServer = Array.Find(_config.SavedServers, s => s.Address == serverUrl);
            if (existingServer == null)
            {
                var newServer = new SavedServer
                {
                    Name = "VMS Production Server",
                    Address = serverUrl,
                    LastUsed = DateTime.UtcNow
                };
                
                var serverList = new List<SavedServer>(_config.SavedServers) { newServer };
                _config.SavedServers = serverList.ToArray();
            }
            else
            {
                existingServer.LastUsed = DateTime.UtcNow;
            }
            
            SaveConfiguration();
        }

        private ClientConfiguration CreateDefaultConfiguration()
        {
            return new ClientConfiguration
            {
                Settings = new ClientSettings
                {
                    AutoDiscovery = true,
                    DefaultTimeout = 5000,
                    PreferredServer = "",
                    LastConnectedServer = "",
                    WindowWidth = 600,
                    WindowHeight = 500,
                    StartMinimized = false
                },
                SavedServers = Array.Empty<SavedServer>(),
                LastUpdated = DateTime.UtcNow
            };
        }
    }

    public class ClientConfiguration
    {
        [JsonProperty("Settings")]
        public ClientSettings Settings { get; set; } = new();

        [JsonProperty("SavedServers")]
        public SavedServer[] SavedServers { get; set; } = Array.Empty<SavedServer>();

        [JsonProperty("LastUpdated")]
        public DateTime LastUpdated { get; set; }
    }

    public class ClientSettings
    {
        [JsonProperty("AutoDiscovery")]
        public bool AutoDiscovery { get; set; } = true;

        [JsonProperty("DefaultTimeout")]
        public int DefaultTimeout { get; set; } = 5000;

        [JsonProperty("PreferredServer")]
        public string PreferredServer { get; set; } = "";

        [JsonProperty("LastConnectedServer")]
        public string LastConnectedServer { get; set; } = "";

        [JsonProperty("WindowWidth")]
        public int WindowWidth { get; set; } = 600;

        [JsonProperty("WindowHeight")]
        public int WindowHeight { get; set; } = 500;

        [JsonProperty("StartMinimized")]
        public bool StartMinimized { get; set; } = false;
    }

    public class SavedServer
    {
        [JsonProperty("Name")]
        public string Name { get; set; } = "";

        [JsonProperty("Address")]
        public string Address { get; set; } = "";

        [JsonProperty("LastUsed")]
        public DateTime LastUsed { get; set; }
    }
}
