/**
 * VMS Server Discovery Service
 * Automatically discovers VMS servers on the network using multiple methods
 */

export interface VMSServerInfo {
  serviceName: string;
  host: string;
  port: number;
  version: string;
  timestamp: number;
  capabilities: string[];
  url: string;
  ping?: number;
  lastSeen?: number;
}

export interface DiscoveryResult {
  servers: VMSServerInfo[];
  bestServer: VMSServerInfo | null;
  discoveryMethod: string;
}

class ServerDiscoveryService {
  private discoveredServers = new Map<string, VMSServerInfo>();
  private discoveryCallbacks: ((result: DiscoveryResult) => void)[] = [];
  private isDiscovering = false;
  private discoveryTimeout: NodeJS.Timeout | null = null;

  /**
   * Start server discovery process
   */
  async discoverServers(): Promise<DiscoveryResult> {
    if (this.isDiscovering) {
      return this.getCurrentResult();
    }

    this.isDiscovering = true;
    this.discoveredServers.clear();
    this.updateStatus('Starting server discovery...');

    try {
      // Method 1: Try common localhost ports
      await this.tryCommonPorts();
      
      // Method 2: Try network broadcast discovery
      await this.tryNetworkDiscovery();
      
      // Method 3: Try known IP ranges
      await this.tryIPRangeScanning();

      const result = this.getCurrentResult();
      this.notifyCallbacks(result);
      return result;

    } finally {
      this.isDiscovering = false;
    }
  }

  /**
   * Try common localhost ports first
   */
  private async tryCommonPorts(): Promise<void> {
    this.updateStatus('Checking localhost ports...');
    const commonPorts = [8080, 3000, 8000, 8081, 3001];
    
    const promises = commonPorts.map(port => 
      this.testServer('localhost', port, 'localhost-scan')
    );
    
    await Promise.allSettled(promises);
  }

  /**
   * Try network broadcast discovery
   */
  private async tryNetworkDiscovery(): Promise<void> {
    this.updateStatus('Scanning network for VMS servers...');
    
    try {
      // Try to get network info from browser (limited)
      const networkHints = this.getNetworkHints();
      
      for (const hint of networkHints) {
        await this.testServer(hint.host, hint.port, 'network-scan');
      }
    } catch (error) {
      console.warn('Network discovery failed:', error);
    }
  }

  /**
   * Try scanning common IP ranges (limited scan for performance)
   */
  private async tryIPRangeScanning(): Promise<void> {
    this.updateStatus('Scanning common IP ranges...');

    // Limited IP ranges for faster discovery
    const targets = [
      '************', // Known server IP
      '***********',
      '***********',
      '********',
    ];

    const promises = targets.map(ip =>
      this.testServer(ip, 8080, 'ip-scan')
    );

    await Promise.allSettled(promises);
  }

  /**
   * Test if a server is running VMS at the given host:port
   */
  private async testServer(host: string, port: number, method: string): Promise<void> {
    const url = `http://${host}:${port}`;
    const startTime = Date.now();
    
    try {
      // Test health endpoint with short timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 2000);
      
      const response = await fetch(`${url}/api/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        },
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        const data = await response.json();
        const ping = Date.now() - startTime;
        
        // Verify this is actually a VMS server
        if (data.service === 'vms-server' || data.serviceName === 'VMS-Server') {
          const serverInfo: VMSServerInfo = {
            serviceName: data.serviceName || 'VMS-Server',
            host,
            port,
            version: data.version || '1.0.0',
            timestamp: data.timestamp ? new Date(data.timestamp).getTime() : Date.now(),
            capabilities: data.capabilities || ['voucher-management'],
            url,
            ping,
            lastSeen: Date.now()
          };
          
          this.discoveredServers.set(`${host}:${port}`, serverInfo);
          this.updateStatus(`Found VMS server at ${url} (${ping}ms)`);
          console.log(`✅ VMS Server discovered: ${url} via ${method}`);
        }
      }
    } catch (error) {
      // Silently ignore connection errors during discovery
    }
  }

  /**
   * Get network hints from various sources
   */
  private getNetworkHints(): { host: string; port: number }[] {
    const hints: { host: string; port: number }[] = [];
    
    // Add current page host if different from localhost
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
      hints.push({ host: window.location.hostname, port: 8080 });
    }
    
    // Add common development IPs
    hints.push(
      { host: '127.0.0.1', port: 8080 },
      { host: '0.0.0.0', port: 8080 },
      { host: '************', port: 8080 }, // Current known server IP
    );
    
    return hints;
  }

  /**
   * Get current discovery result
   */
  private getCurrentResult(): DiscoveryResult {
    const servers = Array.from(this.discoveredServers.values());
    const bestServer = this.selectBestServer(servers);
    
    return {
      servers,
      bestServer,
      discoveryMethod: servers.length > 0 ? 'auto-discovery' : 'none'
    };
  }

  /**
   * Select the best server from discovered servers
   */
  private selectBestServer(servers: VMSServerInfo[]): VMSServerInfo | null {
    if (servers.length === 0) return null;
    
    // Prefer localhost/127.0.0.1 for development
    const localhost = servers.find(s => 
      s.host === 'localhost' || s.host === '127.0.0.1'
    );
    if (localhost) return localhost;
    
    // Otherwise, prefer the one with lowest ping
    return servers.reduce((best, current) => {
      if (!best.ping || !current.ping) return best;
      return current.ping < best.ping ? current : best;
    });
  }

  /**
   * Subscribe to discovery updates
   */
  onDiscoveryUpdate(callback: (result: DiscoveryResult) => void): () => void {
    this.discoveryCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.discoveryCallbacks.indexOf(callback);
      if (index > -1) {
        this.discoveryCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Notify all callbacks of discovery updates
   */
  private notifyCallbacks(result: DiscoveryResult): void {
    this.discoveryCallbacks.forEach(callback => {
      try {
        callback(result);
      } catch (error) {
        console.error('Discovery callback error:', error);
      }
    });
  }

  /**
   * Update status display
   */
  private updateStatus(message: string): void {
    const statusElement = document.getElementById('server-status');
    if (statusElement) {
      statusElement.textContent = message;
    }
    console.log(`🔍 Discovery: ${message}`);
  }

  /**
   * Get cached servers
   */
  getCachedServers(): VMSServerInfo[] {
    return Array.from(this.discoveredServers.values());
  }

  /**
   * Clear discovery cache
   */
  clearCache(): void {
    this.discoveredServers.clear();
  }
}

export const serverDiscovery = new ServerDiscoveryService();
