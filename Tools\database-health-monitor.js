/**
 * VMS Production Database Health Monitor
 * Prevents critical database issues by monitoring table existence and integrity
 */

const mysql = require('../Server/node_modules/mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

class DatabaseHealthMonitor {
  constructor() {
    this.config = {
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production',
      charset: 'utf8mb4'
    };
    
    this.requiredTables = [
      'users',
      'vouchers',
      'voucher_batches', // Updated to match actual table name
      'audit_voucher_attachments', // Critical table that was missing
      'user_sessions',
      'system_settings'
    ];
    
    this.logFile = path.join(__dirname, '../Server/logs/database-health.log');
  }

  async log(level, message) {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} [${level.toUpperCase()}] ${message}\n`;
    
    try {
      await fs.appendFile(this.logFile, logEntry);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
    
    console.log(`[DB-HEALTH] ${logEntry.trim()}`);
  }

  async checkTableExists(connection, tableName) {
    try {
      const [rows] = await connection.execute(
        `SELECT COUNT(*) as count FROM information_schema.TABLES 
         WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?`,
        [this.config.database, tableName]
      );
      
      return rows[0].count > 0;
    } catch (error) {
      await this.log('error', `Failed to check table ${tableName}: ${error.message}`);
      return false;
    }
  }

  async createMissingTable(connection, tableName) {
    const tableSchemas = {
      audit_voucher_attachments: `
        CREATE TABLE audit_voucher_attachments (
          id varchar(36) NOT NULL,
          voucher_id varchar(36) NOT NULL,
          original_filename varchar(255) NOT NULL,
          stored_filename varchar(255) NOT NULL,
          file_path varchar(500) NOT NULL,
          file_size bigint NOT NULL,
          mime_type varchar(100) NOT NULL,
          uploaded_by varchar(36) NOT NULL,
          uploaded_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          is_active tinyint(1) NOT NULL DEFAULT 1,
          PRIMARY KEY (id),
          KEY idx_voucher_id (voucher_id),
          KEY idx_uploaded_by (uploaded_by),
          KEY idx_is_active (is_active),
          KEY idx_uploaded_at (uploaded_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `
    };

    if (tableSchemas[tableName]) {
      try {
        await connection.execute(tableSchemas[tableName]);
        await this.log('info', `CRITICAL FIX: Created missing table ${tableName}`);
        return true;
      } catch (error) {
        await this.log('error', `Failed to create table ${tableName}: ${error.message}`);
        return false;
      }
    } else {
      await this.log('error', `No schema defined for missing table: ${tableName}`);
      return false;
    }
  }

  async checkDatabaseHealth() {
    let connection;
    
    try {
      await this.log('info', 'Starting database health check...');
      
      connection = await mysql.createConnection(this.config);
      
      const results = {
        healthy: true,
        missingTables: [],
        errors: []
      };

      // Check all required tables
      for (const tableName of this.requiredTables) {
        const exists = await this.checkTableExists(connection, tableName);
        
        if (!exists) {
          await this.log('error', `CRITICAL: Missing table ${tableName}`);
          results.missingTables.push(tableName);
          results.healthy = false;
          
          // Attempt to create the missing table
          const created = await this.createMissingTable(connection, tableName);
          if (created) {
            await this.log('info', `Successfully created missing table ${tableName}`);
          }
        } else {
          await this.log('info', `Table ${tableName}: OK`);
        }
      }

      // Check database connectivity
      await connection.execute('SELECT 1');
      await this.log('info', 'Database connectivity: OK');

      // Check disk space for uploads directory
      try {
        const uploadsDir = path.join(__dirname, '../uploads');
        await fs.access(uploadsDir);
        await this.log('info', 'Uploads directory: OK');
      } catch (error) {
        await this.log('error', `Uploads directory issue: ${error.message}`);
        results.errors.push('uploads_directory');
        results.healthy = false;
      }

      if (results.healthy) {
        await this.log('info', '✅ Database health check PASSED - All systems operational');
      } else {
        await this.log('error', '❌ Database health check FAILED - Issues detected and fixed');
      }

      return results;

    } catch (error) {
      await this.log('error', `Database health check failed: ${error.message}`);
      return {
        healthy: false,
        missingTables: [],
        errors: [error.message]
      };
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async startMonitoring(intervalMinutes = 30) {
    await this.log('info', `Starting database health monitoring (every ${intervalMinutes} minutes)`);
    
    // Initial check
    await this.checkDatabaseHealth();
    
    // Schedule periodic checks
    setInterval(async () => {
      await this.checkDatabaseHealth();
    }, intervalMinutes * 60 * 1000);
  }
}

// Export for use as module
module.exports = DatabaseHealthMonitor;

// Run directly if called from command line
if (require.main === module) {
  const monitor = new DatabaseHealthMonitor();
  
  if (process.argv.includes('--monitor')) {
    // Start continuous monitoring
    monitor.startMonitoring(30); // Check every 30 minutes
  } else {
    // Run single health check
    monitor.checkDatabaseHealth()
      .then(results => {
        console.log('\n=== DATABASE HEALTH REPORT ===');
        console.log(`Status: ${results.healthy ? '✅ HEALTHY' : '❌ ISSUES DETECTED'}`);
        
        if (results.missingTables.length > 0) {
          console.log(`Missing Tables: ${results.missingTables.join(', ')}`);
        }
        
        if (results.errors.length > 0) {
          console.log(`Errors: ${results.errors.join(', ')}`);
        }
        
        process.exit(results.healthy ? 0 : 1);
      })
      .catch(error => {
        console.error('Health check failed:', error);
        process.exit(1);
      });
  }
}
