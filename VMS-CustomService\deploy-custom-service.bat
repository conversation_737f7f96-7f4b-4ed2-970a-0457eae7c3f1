@echo off
echo ========================================
echo VMS Custom Service Deployment
echo ========================================

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

set SERVICE_NAME=VMS-Production-Service
set SERVICE_DISPLAY_NAME=VMS Production Service
set INSTALL_PATH=%~dp0..\Service-Custom
set SOURCE_PATH=%~dp0bin\Release\net8.0-windows\win-x64\publish

echo [1/6] Checking build output...
if not exist "%SOURCE_PATH%\VMS-CustomService.exe" (
    echo ERROR: Service executable not found. Please run 'build-service.bat' first.
    pause
    exit /b 1
)

echo [2/6] Stopping existing service (if running)...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Stopping existing service...
    sc stop "%SERVICE_NAME%"
    timeout /t 5 /nobreak >nul
    echo Deleting existing service...
    sc delete "%SERVICE_NAME%"
    timeout /t 2 /nobreak >nul
)

echo [3/6] Creating installation directory...
if not exist "%INSTALL_PATH%" (
    mkdir "%INSTALL_PATH%"
)

echo [4/6] Copying service files...
xcopy /E /I /Y "%SOURCE_PATH%\*" "%INSTALL_PATH%\"
if %errorLevel% neq 0 (
    echo ERROR: Failed to copy service files
    pause
    exit /b 1
)

echo [5/6] Installing Windows Service...
sc create "%SERVICE_NAME%" binPath= "\"%INSTALL_PATH%\VMS-CustomService.exe\"" DisplayName= "%SERVICE_DISPLAY_NAME%" start= auto
if %errorLevel% neq 0 (
    echo ERROR: Failed to install service
    pause
    exit /b 1
)

echo [6/6] Starting service...
sc start "%SERVICE_NAME%"
if %errorLevel% neq 0 (
    echo WARNING: Service installed but failed to start
    echo Check the Event Log for details
) else (
    echo SUCCESS: VMS Custom Service installed and started!
)

echo.
echo Service Details:
echo - Name: %SERVICE_NAME%
echo - Display Name: %SERVICE_DISPLAY_NAME%
echo - Install Path: %INSTALL_PATH%
echo - Startup Type: Automatic
echo.
echo Use 'manage-custom-service.bat' to control the service
echo.
pause
