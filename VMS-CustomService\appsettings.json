{"VMS": {"ServerPath": "C:\\\\VMS-PRODUCTION\\\\Server", "NodeExecutable": "node", "ServerScript": "dist\\index.js", "Port": 8080, "HealthCheckInterval": 30000, "RestartDelay": 5000, "MaxRestartAttempts": 3, "HealthCheckUrl": "http://localhost:8080/health", "ProcessTimeout": 30000}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "EventLog": {"LogLevel": {"Default": "Information"}, "SourceName": "VMS-Production-Service"}}}