@echo off
echo ===================================
echo Building VMS Client Application
echo ===================================

REM Clean previous builds
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"

echo.
echo Restoring NuGet packages...
dotnet restore

echo.
echo Building application...
dotnet build --configuration Release

echo.
echo Publishing self-contained executable...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "publish\win-x64" -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true

echo.
echo ===================================
echo Build Complete!
echo ===================================
echo.
echo Executable location: publish\win-x64\VMS-Client.exe
echo.
echo You can now copy VMS-Client.exe to any Windows 10/11 PC
echo No installation required - just run the .exe file
echo.
pause
