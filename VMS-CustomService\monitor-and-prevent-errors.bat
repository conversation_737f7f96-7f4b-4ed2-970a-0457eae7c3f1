@echo off
echo ========================================
echo VMS Production Error Prevention Monitor
echo ========================================

set LOG_FILE=error_prevention_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOG_FILE=%LOG_FILE: =0%

echo [%date% %time%] Starting VMS Error Prevention Monitor >> %LOG_FILE%

:monitor_loop
echo.
echo [%date% %time%] === VMS HEALTH CHECK ===

REM Check 1: Service Status
echo [1/7] Checking VMS Service Status...
sc query VMS-Production-Service | findstr "STATE" | findstr "RUNNING" >nul
if %errorlevel% neq 0 (
    echo ERROR: VMS Service is not running! >> %LOG_FILE%
    echo WARNING: VMS Service is not running!
    echo Attempting to restart service...
    sc start VMS-Production-Service
    timeout /t 10 /nobreak >nul
) else (
    echo ✓ VMS Service is running
)

REM Check 2: Port 8080 Accessibility
echo [2/7] Checking VMS Server Port...
netstat -ano | findstr ":8080" >nul
if %errorlevel% neq 0 (
    echo ERROR: Port 8080 is not listening! >> %LOG_FILE%
    echo WARNING: VMS Server port not accessible!
) else (
    echo ✓ Port 8080 is accessible
)

REM Check 3: Health Endpoint
echo [3/7] Checking VMS Health Endpoint...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/health' -UseBasicParsing -TimeoutSec 5; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Health endpoint not responding! >> %LOG_FILE%
    echo WARNING: VMS Health endpoint failed!
) else (
    echo ✓ Health endpoint responding
)

REM Check 4: Error Log Analysis
echo [4/7] Checking for new errors...
powershell -Command "if (Test-Path 'C:\Program Files\DEPLOYMENT PACKAGE\Server\logs\error.log') { $errors = Get-Content 'C:\Program Files\DEPLOYMENT PACKAGE\Server\logs\error.log' -Tail 5 | Where-Object { $_ -match 'error' -and $_ -match (Get-Date).ToString('yyyy-MM-dd') }; if ($errors) { Write-Host 'WARNING: Recent errors detected!'; $errors | ForEach-Object { Write-Host $_ } } else { Write-Host '✓ No recent errors detected' } } else { Write-Host 'WARNING: Error log not found!' }"

REM Check 5: Database Connection
echo [5/7] Checking database connectivity...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/users/online' -UseBasicParsing -TimeoutSec 5; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Database connectivity issue! >> %LOG_FILE%
    echo WARNING: Database connection failed!
) else (
    echo ✓ Database connectivity OK
)

REM Check 6: Memory Usage
echo [6/7] Checking memory usage...
for /f "tokens=2 delims=," %%a in ('tasklist /fi "imagename eq node.exe" /fo csv ^| findstr "node.exe"') do (
    set memory=%%a
    set memory=!memory:"=!
    echo VMS Memory Usage: !memory!
)

REM Check 7: Disk Space
echo [7/7] Checking disk space...
for /f "tokens=3" %%a in ('dir C:\ /-c ^| findstr "bytes free"') do (
    echo Free Disk Space: %%a bytes
)

echo.
echo [%date% %time%] Health check completed >> %LOG_FILE%

REM Wait 5 minutes before next check
echo Next check in 5 minutes...
timeout /t 300 /nobreak >nul

goto monitor_loop
