import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App'
import './index.css'

// Initialize the application
async function initializeApp() {
  const loadingScreen = document.getElementById('loading-screen');
  const statusElement = document.getElementById('server-status');
  
  try {
    // Update loading status
    if (statusElement) {
      statusElement.textContent = 'Initializing VMS Client...';
    }

    // Import services dynamically to show loading progress
    const { serverDiscovery } = await import('./services/ServerDiscovery');
    const { apiClient } = await import('./services/ApiClient');
    const { webSocketService } = await import('./services/WebSocketService');

    if (statusElement) {
      statusElement.textContent = 'Discovering VMS servers...';
    }

    // Discover and connect to VMS server
    const discoveryResult = await serverDiscovery.discoverServers();
    
    if (!discoveryResult.bestServer) {
      throw new Error('No VMS servers found on the network');
    }

    if (statusElement) {
      statusElement.textContent = `Found server at ${discoveryResult.bestServer.url}`;
    }

    // Initialize API client
    const apiInitialized = await apiClient.initialize();
    if (!apiInitialized) {
      throw new Error('Failed to initialize API client');
    }

    if (statusElement) {
      statusElement.textContent = 'Connecting WebSocket...';
    }

    // Initialize WebSocket (non-blocking)
    webSocketService.connect().catch(error => {
      console.warn('WebSocket connection failed, continuing without real-time updates:', error);
    });

    if (statusElement) {
      statusElement.textContent = 'Starting application...';
    }

    // Small delay to show the final status
    await new Promise(resolve => setTimeout(resolve, 500));

    // Hide loading screen
    if (loadingScreen) {
      loadingScreen.style.opacity = '0';
      loadingScreen.style.transition = 'opacity 0.5s ease-out';
      
      setTimeout(() => {
        loadingScreen.remove();
      }, 500);
    }

    // Render the React app
    const root = ReactDOM.createRoot(document.getElementById('root')!);
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );

    console.log('✅ VMS Client initialized successfully');

  } catch (error) {
    console.error('❌ Failed to initialize VMS Client:', error);
    
    // Show error in loading screen
    if (statusElement) {
      statusElement.innerHTML = `
        <div style="color: #ff6b6b; text-align: center;">
          <div>❌ Failed to connect to VMS server</div>
          <div style="margin-top: 10px; font-size: 11px;">
            ${error instanceof Error ? error.message : 'Unknown error'}
          </div>
          <div style="margin-top: 15px;">
            <button onclick="window.location.reload()" 
                    style="padding: 8px 16px; background: rgba(255,255,255,0.2); 
                           border: 1px solid rgba(255,255,255,0.3); border-radius: 4px; 
                           color: white; cursor: pointer;">
              Retry Connection
            </button>
          </div>
        </div>
      `;
    }
  }
}

// Start the application
initializeApp();
