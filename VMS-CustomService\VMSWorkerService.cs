using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Net.Http;
using System.Text.Json;

namespace VMSCustomService;

public class VMSWorkerService : BackgroundService
{
    private readonly ILogger<VMSWorkerService> _logger;
    private readonly VMSConfiguration _config;
    private readonly HttpClient _httpClient;
    private Process? _vmsProcess;
    private int _restartAttempts = 0;
    private DateTime _lastRestartTime = DateTime.MinValue;

    public VMSWorkerService(ILogger<VMSWorkerService> logger, IOptions<VMSConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;
        _httpClient = new HttpClient();
        _httpClient.Timeout = TimeSpan.FromMilliseconds(_config.ProcessTimeout);
    }

    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("VMS Production Service starting...");
        
        // Validate configuration
        if (!Directory.Exists(_config.ServerPath))
        {
            _logger.LogError("VMS Server path does not exist: {ServerPath}", _config.ServerPath);
            throw new DirectoryNotFoundException($"VMS Server path not found: {_config.ServerPath}");
        }

        var serverScriptPath = Path.Combine(_config.ServerPath, _config.ServerScript);
        if (!File.Exists(serverScriptPath))
        {
            _logger.LogError("VMS Server script does not exist: {ServerScript}", serverScriptPath);
            throw new FileNotFoundException($"VMS Server script not found: {serverScriptPath}");
        }

        _logger.LogInformation("VMS Service configuration validated successfully");
        _logger.LogInformation("Server Path: {ServerPath}", _config.ServerPath);
        _logger.LogInformation("Health Check URL: {HealthCheckUrl}", _config.HealthCheckUrl);
        
        await base.StartAsync(cancellationToken);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("VMS Service worker started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Check if VMS process is running
                if (_vmsProcess == null || _vmsProcess.HasExited)
                {
                    _logger.LogWarning("VMS server process is not running. Starting...");
                    await StartVMSServer();
                }
                else
                {
                    // Perform health check
                    var isHealthy = await PerformHealthCheck();
                    if (!isHealthy)
                    {
                        _logger.LogWarning("VMS server health check failed. Restarting...");
                        await RestartVMSServer();
                    }
                    else
                    {
                        _logger.LogDebug("VMS server health check passed");
                        _restartAttempts = 0; // Reset restart attempts on successful health check
                    }
                }

                await Task.Delay(_config.HealthCheckInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in VMS service worker loop");
                await Task.Delay(5000, stoppingToken); // Wait before retrying
            }
        }
    }

    private async Task StartVMSServer()
    {
        try
        {
            _logger.LogInformation("Starting VMS server...");

            var startInfo = new ProcessStartInfo
            {
                FileName = _config.NodeExecutable,
                Arguments = _config.ServerScript,
                WorkingDirectory = _config.ServerPath,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            _vmsProcess = new Process { StartInfo = startInfo };
            
            // Set up event handlers for output
            _vmsProcess.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                    _logger.LogInformation("VMS Output: {Output}", e.Data);
            };
            
            _vmsProcess.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                    _logger.LogError("VMS Error: {Error}", e.Data);
            };

            _vmsProcess.Start();
            _vmsProcess.BeginOutputReadLine();
            _vmsProcess.BeginErrorReadLine();

            _logger.LogInformation("VMS server started with PID: {ProcessId}", _vmsProcess.Id);
            
            // Wait a moment for the server to initialize
            await Task.Delay(5000);
            
            _lastRestartTime = DateTime.Now;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start VMS server");
            throw;
        }
    }

    private async Task<bool> PerformHealthCheck()
    {
        try
        {
            var response = await _httpClient.GetAsync(_config.HealthCheckUrl);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var healthData = JsonSerializer.Deserialize<JsonElement>(content);

                if (healthData.TryGetProperty("status", out var status) &&
                    status.GetString() == "healthy")
                {
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Health check failed");
            return false;
        }
    }

    private async Task RestartVMSServer()
    {
        if (_restartAttempts >= _config.MaxRestartAttempts)
        {
            _logger.LogError("Maximum restart attempts ({MaxAttempts}) reached. Service will stop.",
                _config.MaxRestartAttempts);
            return;
        }

        _restartAttempts++;
        _logger.LogInformation("Restarting VMS server (attempt {Attempt}/{MaxAttempts})",
            _restartAttempts, _config.MaxRestartAttempts);

        await StopVMSServer();
        await Task.Delay(_config.RestartDelay);
        await StartVMSServer();
    }

    private async Task StopVMSServer()
    {
        if (_vmsProcess != null && !_vmsProcess.HasExited)
        {
            try
            {
                _logger.LogInformation("Stopping VMS server...");
                _vmsProcess.Kill(true); // Kill process tree
                await _vmsProcess.WaitForExitAsync();
                _logger.LogInformation("VMS server stopped");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping VMS server");
            }
        }
        _vmsProcess?.Dispose();
        _vmsProcess = null;
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("VMS Production Service stopping...");
        await StopVMSServer();
        _httpClient.Dispose();
        await base.StopAsync(cancellationToken);
        _logger.LogInformation("VMS Production Service stopped");
    }
}
