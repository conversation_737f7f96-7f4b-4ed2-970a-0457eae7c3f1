using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using VMS_Client.Services;
using System.Net.Http;

namespace VMS_Client
{
    public partial class MainForm : Form
    {
        private readonly ConfigurationService _configService;
        private readonly ServerDiscoveryService _discoveryService;
        private Panel _discoveryPanel;
        private Label _titleLabel;
        private Label _statusLabel;
        private Label _instructionLabel;
        private Button _connectButton;
        private Label _versionLabel;
        private PictureBox _logoBox;

        public MainForm(ConfigurationService configService, ServerDiscoveryService discoveryService)
        {
            _configService = configService;
            _discoveryService = discoveryService;
            
            InitializeComponent();
            SetupDiscoveryInterface();
            
            // Subscribe to discovery events
            _discoveryService.ServerFound += OnServerFound;
            _discoveryService.DiscoveryFailed += OnDiscoveryFailed;

            // Don't start discovery automatically - wait for user to click Connect
        }

        private void InitializeComponent()
        {
            this.Text = "VMS MAIN CLIENT";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.BackColor = Color.White;
            this.Icon = CreateVmsIcon();
        }

        private void SetupDiscoveryInterface()
        {
            // Main discovery panel - simple and clean
            _discoveryPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };

            // VMS Logo (Blue box with white VMS text) - fixed position, no events
            _logoBox = new PictureBox
            {
                Size = new Size(100, 100),
                Location = new Point(250, 30), // Fixed center position
                BackColor = Color.FromArgb(51, 122, 183), // Bootstrap blue
                Anchor = AnchorStyles.None
            };
            
            // VMS text will be drawn by LogoBox_Paint method

            // Title label - fixed position, no events
            _titleLabel = new Label
            {
                Text = "VMS MAIN CLIENT",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 122, 183),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(400, 30),
                Location = new Point(100, 150),
                Anchor = AnchorStyles.None
            };

            // Status label - fixed position, no events
            _statusLabel = new Label
            {
                Text = "Ready to connect to VMS server",
                Font = new Font("Arial", 12),
                ForeColor = Color.FromArgb(102, 102, 102),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(400, 25),
                Location = new Point(100, 190),
                Anchor = AnchorStyles.None
            };

            // Instruction label - fixed position, no events
            _instructionLabel = new Label
            {
                Text = "Click 'Connect to VMS' to find server on network",
                Font = new Font("Arial", 10),
                ForeColor = Color.FromArgb(153, 153, 153),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(400, 20),
                Location = new Point(100, 220),
                Anchor = AnchorStyles.None
            };

            // Connect button - fixed position, single click event only
            _connectButton = new Button
            {
                Text = "Connect to VMS",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(225, 260),
                BackColor = Color.FromArgb(51, 122, 183),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.None
            };
            _connectButton.FlatAppearance.BorderSize = 0;
            _connectButton.Click += ConnectButton_Click;

            // Version label
            _versionLabel = new Label
            {
                Text = "VMS Main Client v4.0 - Network Discovery Edition",
                Font = new Font("Arial", 9),
                ForeColor = Color.FromArgb(153, 153, 153),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(400, 20),
                Location = new Point(100, 400), // Manually centered
                Anchor = AnchorStyles.None
            };

            // Add VMS text to logo - single paint event only
            _logoBox.Paint += LogoBox_Paint;

            // Add controls to discovery panel
            _discoveryPanel.Controls.Add(_logoBox);
            _discoveryPanel.Controls.Add(_titleLabel);
            _discoveryPanel.Controls.Add(_statusLabel);
            _discoveryPanel.Controls.Add(_instructionLabel);
            _discoveryPanel.Controls.Add(_connectButton);
            _discoveryPanel.Controls.Add(_versionLabel);

            this.Controls.Add(_discoveryPanel);
        }

        private void ConnectButton_Click(object sender, EventArgs e)
        {
            _connectButton.Enabled = false;
            _connectButton.Text = "Searching...";
            _statusLabel.Text = "Searching for VMS server...";
            _instructionLabel.Text = "Please wait while we locate the server";

            _discoveryService.StartDiscovery();
        }

        private void OnServerFound(object sender, ServerFoundEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnServerFound(sender, e)));
                return;
            }

            // Hide discovery interface and show web view
            ShowWebInterface(e.ServerUrl);
        }

        private void OnDiscoveryFailed(object sender, EventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnDiscoveryFailed(sender, e)));
                return;
            }

            _connectButton.Enabled = true;
            _connectButton.Text = "Connect to VMS";
            _statusLabel.Text = "Could not find VMS server";
            _instructionLabel.Text = "Please check network connection and try again";
        }

        private void ShowWebInterface(string serverUrl)
        {
            try
            {
                // Update status to show connection success
                _statusLabel.Text = $"Connected to VMS server: {serverUrl}";
                _instructionLabel.Text = "Opening VMS in your browser...";
                _connectButton.Text = "Connected ✓";
                _connectButton.BackColor = Color.FromArgb(92, 184, 92); // Green color

                // Update window title
                this.Text = $"VMS MAIN CLIENT - Connected to {serverUrl}";

                // Open VMS in external browser (preferably Chrome)
                OpenInBrowser(serverUrl);

                // Wait a moment for browser to open, then close the app
                var timer = new System.Windows.Forms.Timer();
                timer.Interval = 2000; // 2 seconds
                timer.Tick += (s, e) =>
                {
                    timer.Stop();
                    timer.Dispose();
                    this.Close();
                };
                timer.Start();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open VMS in browser: {ex.Message}",
                    "Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Reset discovery interface
                OnDiscoveryFailed(this, EventArgs.Empty);
            }
        }

        private void OpenInBrowser(string url)
        {
            try
            {
                // Try to open in Chrome first
                var chromeProcesses = new[]
                {
                    @"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    Environment.ExpandEnvironmentVariables(@"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe")
                };

                foreach (var chromePath in chromeProcesses)
                {
                    if (File.Exists(chromePath))
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = chromePath,
                            Arguments = $"\"{url}\"",
                            UseShellExecute = false
                        });
                        return;
                    }
                }

                // If Chrome not found, use default browser
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                // Fallback: try opening with default browser using shell execute
                try
                {
                    System.Diagnostics.Process.Start(url);
                }
                catch
                {
                    throw new Exception($"Could not open browser. Please manually navigate to: {url}");
                }
            }
        }

        private Icon CreateVmsIcon()
        {
            // Create a simple VMS icon
            var bitmap = new Bitmap(32, 32);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.FillRectangle(new SolidBrush(Color.FromArgb(51, 122, 183)), 0, 0, 32, 32);
                using (var font = new Font("Arial", 10, FontStyle.Bold))
                using (var brush = new SolidBrush(Color.White))
                {
                    g.DrawString("VMS", font, brush, 2, 8);
                }
            }
            return Icon.FromHandle(bitmap.GetHicon());
        }

        private void LogoBox_Paint(object sender, PaintEventArgs e)
        {
            using (var font = new Font("Arial", 24, FontStyle.Bold))
            using (var brush = new SolidBrush(Color.White))
            {
                var text = "VMS";
                var size = e.Graphics.MeasureString(text, font);
                var x = (_logoBox.Width - size.Width) / 2;
                var y = (_logoBox.Height - size.Height) / 2;
                e.Graphics.DrawString(text, font, brush, x, y);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _discoveryService?.StopDiscovery();
            base.OnFormClosing(e);
        }
    }
}
