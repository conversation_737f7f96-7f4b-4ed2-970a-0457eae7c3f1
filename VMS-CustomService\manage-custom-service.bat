@echo off
:menu
cls
echo ========================================
echo VMS Custom Service Management
echo ========================================
echo.
echo 1. Start Service
echo 2. Stop Service
echo 3. Restart Service
echo 4. Check Service Status
echo 5. View Event Logs
echo 6. Uninstall Service
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto start_service
if "%choice%"=="2" goto stop_service
if "%choice%"=="3" goto restart_service
if "%choice%"=="4" goto status_service
if "%choice%"=="5" goto view_logs
if "%choice%"=="6" goto uninstall_service
if "%choice%"=="7" goto exit
goto menu

:start_service
echo Starting VMS Service...
sc start VMS-Production-Service
echo.
pause
goto menu

:stop_service
echo Stopping VMS Service...
sc stop VMS-Production-Service
echo.
pause
goto menu

:restart_service
echo Restarting VMS Service...
sc stop VMS-Production-Service
timeout /t 3 /nobreak >nul
sc start VMS-Production-Service
echo.
pause
goto menu

:status_service
echo VMS Service Status:
echo.
sc query VMS-Production-Service
echo.
echo VMS Server Health Check:
curl -s http://localhost:8080/health 2>nul || echo Health check failed or server not responding
echo.
pause
goto menu

:view_logs
echo Recent VMS Service Events:
echo.
powershell -Command "Get-EventLog -LogName Application -Source 'VMS-Production-Service' -Newest 10 -ErrorAction SilentlyContinue | Format-Table TimeGenerated, EntryType, Message -Wrap"
echo.
pause
goto menu

:uninstall_service
echo WARNING: This will completely remove the VMS Service!
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo Stopping service...
sc stop VMS-Production-Service
timeout /t 3 /nobreak >nul

echo Uninstalling service...
sc delete VMS-Production-Service

echo Removing service files...
if exist "%~dp0..\Service-Custom" (
    rmdir /s /q "%~dp0..\Service-Custom"
)

echo VMS Service uninstalled successfully!
echo.
pause
goto menu

:exit
exit /b 0
