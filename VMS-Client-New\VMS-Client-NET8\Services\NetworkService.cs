using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;

namespace VMS.Client.Services;

public class NetworkService : INetworkService
{
    private readonly ILogger<NetworkService> _logger;

    public NetworkService(ILogger<NetworkService> logger)
    {
        _logger = logger;
    }

    public async Task<List<string>> GetLocalNetworkRangesAsync()
    {
        var ranges = new List<string>();
        
        try
        {
            var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                .Where(ni => ni.OperationalStatus == OperationalStatus.Up &&
                           ni.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                           ni.NetworkInterfaceType != NetworkInterfaceType.Tunnel);

            foreach (var ni in networkInterfaces)
            {
                var ipProperties = ni.GetIPProperties();
                
                foreach (var unicastAddress in ipProperties.UnicastAddresses)
                {
                    if (unicastAddress.Address.AddressFamily == AddressFamily.InterNetwork)
                    {
                        var ip = unicastAddress.Address.ToString();
                        var subnet = GetSubnetFromIP(ip, unicastAddress.IPv4Mask);
                        
                        if (!string.IsNullOrEmpty(subnet) && !ranges.Contains(subnet))
                        {
                            ranges.Add(subnet);
                            _logger.LogDebug("Found network range: {Subnet}", subnet);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get local network ranges");
        }

        // Add common ranges if none found
        if (ranges.Count == 0)
        {
            ranges.AddRange(new[]
            {
                "192.168.1",
                "192.168.0",
                "10.0.0",
                "10.25.42"
            });
        }

        return ranges;
    }

    public async Task<bool> PingHostAsync(string host, int timeoutMs = 2000)
    {
        try
        {
            using var ping = new Ping();
            var reply = await ping.SendPingAsync(host, timeoutMs);
            return reply.Status == IPStatus.Success;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Ping failed for host: {Host}", host);
            return false;
        }
    }

    public List<string> GetCommonServerIPs()
    {
        return new List<string>
        {
            // Production VMS server IPs
            "************",
            "************",
            
            // Common localhost variations
            "127.0.0.1",
            "localhost",
            
            // Common private network IPs
            "***********",
            "***********00",
            "*************",
            "***********",
            "***********00",
            "*************",
            
            // Common 10.x.x.x ranges
            "********",
            "********00",
            "********",
            "**********",
            
            // Get current machine's IP
            GetLocalIPAddress()
        }.Where(ip => !string.IsNullOrEmpty(ip)).Distinct().ToList();
    }

    private string GetSubnetFromIP(string ip, IPAddress subnetMask)
    {
        try
        {
            var ipBytes = IPAddress.Parse(ip).GetAddressBytes();
            var maskBytes = subnetMask.GetAddressBytes();
            
            // Calculate network address
            var networkBytes = new byte[4];
            for (int i = 0; i < 4; i++)
            {
                networkBytes[i] = (byte)(ipBytes[i] & maskBytes[i]);
            }
            
            var networkAddress = new IPAddress(networkBytes);
            
            // Return first 3 octets for common /24 networks
            var parts = networkAddress.ToString().Split('.');
            if (parts.Length >= 3)
            {
                return $"{parts[0]}.{parts[1]}.{parts[2]}";
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to calculate subnet for IP: {IP}", ip);
        }
        
        return string.Empty;
    }

    private string GetLocalIPAddress()
    {
        try
        {
            var host = Dns.GetHostEntry(Dns.GetHostName());
            var localIP = host.AddressList
                .FirstOrDefault(ip => ip.AddressFamily == AddressFamily.InterNetwork &&
                                    !IPAddress.IsLoopback(ip));
            
            return localIP?.ToString() ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to get local IP address");
            return string.Empty;
        }
    }
}
