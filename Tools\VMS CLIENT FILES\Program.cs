using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace VMSClient
{
    // VMS Server Discovery Models
    public class VMSServerInfo
    {
        public string ServiceName { get; set; } = "";
        public string Host { get; set; } = "";
        public int Port { get; set; }
        public string Version { get; set; } = "";
        public long Timestamp { get; set; }
        public string[] Capabilities { get; set; } = Array.Empty<string>();
        public string Url => $"http://{Host}:{Port}";
    }

    public class VMSDiscoveryMessage
    {
        public string Type { get; set; } = "";
        public VMSServerInfo Service { get; set; } = new();
        public string[] LocalIPs { get; set; } = Array.Empty<string>();
    }

    // UDP Service Discovery Client
    public class VMSServiceDiscovery : IDisposable
    {
        private const int BROADCAST_PORT = 45454;
        private const int DISCOVERY_PORT = 45455;
        private const int CLIENT_PORT = 45456;

        private UdpClient? udpClient;
        private UdpClient? broadcastListener;
        private CancellationTokenSource? cancellationTokenSource;
        private bool isListening = false;

        public event Action<VMSServerInfo>? ServerDiscovered;

        public async Task<VMSServerInfo?> DiscoverServerAsync(int timeoutMs = 10000)
        {
            VMSServerInfo? foundServer = null;
            var tcs = new TaskCompletionSource<VMSServerInfo?>();

            // Set up event handler
            void OnServerDiscovered(VMSServerInfo server)
            {
                foundServer = server;
                tcs.TrySetResult(server);
            }

            ServerDiscovered += OnServerDiscovered;

            try
            {
                await StartListeningAsync();
                await SendDiscoveryRequestAsync();

                // Wait for discovery or timeout
                using var timeoutCts = new CancellationTokenSource(timeoutMs);
                timeoutCts.Token.Register(() => tcs.TrySetResult(null));

                return await tcs.Task;
            }
            finally
            {
                ServerDiscovered -= OnServerDiscovered;
                StopListening();
            }
        }

        private Task StartListeningAsync()
        {
            if (isListening) return Task.CompletedTask;

            try
            {
                cancellationTokenSource = new CancellationTokenSource();

                // Listen for broadcast announcements
                broadcastListener = new UdpClient(BROADCAST_PORT);
                broadcastListener.EnableBroadcast = true;

                // Listen for discovery responses
                udpClient = new UdpClient(CLIENT_PORT);

                isListening = true;

                // Start listening tasks
                _ = Task.Run(() => ListenForBroadcasts(cancellationTokenSource.Token));
                _ = Task.Run(() => ListenForResponses(cancellationTokenSource.Token));

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to start UDP listening: {ex.Message}");
                return Task.FromException(ex);
            }
        }

        private async Task SendDiscoveryRequestAsync()
        {
            try
            {
                var request = new { type = "VMS_DISCOVERY_REQUEST", timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() };
                var message = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(request));

                using var client = new UdpClient();
                client.EnableBroadcast = true;

                // Send to broadcast address
                await client.SendAsync(message, message.Length, new IPEndPoint(IPAddress.Broadcast, DISCOVERY_PORT));

                // Also send to local network broadcast addresses
                var broadcastAddresses = GetBroadcastAddresses();
                foreach (var addr in broadcastAddresses)
                {
                    try
                    {
                        await client.SendAsync(message, message.Length, new IPEndPoint(IPAddress.Parse(addr), DISCOVERY_PORT));
                    }
                    catch { /* Ignore individual broadcast failures */ }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to send discovery request: {ex.Message}");
            }
        }

        private async Task ListenForBroadcasts(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && broadcastListener != null)
            {
                try
                {
                    var result = await broadcastListener.ReceiveAsync();
                    var message = Encoding.UTF8.GetString(result.Buffer);

                    var discoveryMessage = JsonSerializer.Deserialize<VMSDiscoveryMessage>(message);
                    if (discoveryMessage?.Type == "VMS_SERVICE_ANNOUNCEMENT" && discoveryMessage.Service != null)
                    {
                        ServerDiscovered?.Invoke(discoveryMessage.Service);
                    }
                }
                catch (ObjectDisposedException) { break; }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Broadcast listen error: {ex.Message}");
                }
            }
        }

        private async Task ListenForResponses(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && udpClient != null)
            {
                try
                {
                    var result = await udpClient.ReceiveAsync();
                    var message = Encoding.UTF8.GetString(result.Buffer);

                    var discoveryMessage = JsonSerializer.Deserialize<VMSDiscoveryMessage>(message);
                    if (discoveryMessage?.Type == "VMS_DISCOVERY_RESPONSE" && discoveryMessage.Service != null)
                    {
                        ServerDiscovered?.Invoke(discoveryMessage.Service);
                    }
                }
                catch (ObjectDisposedException) { break; }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Response listen error: {ex.Message}");
                }
            }
        }

        private List<string> GetBroadcastAddresses()
        {
            var addresses = new List<string>();

            try
            {
                foreach (var ni in NetworkInterface.GetAllNetworkInterfaces())
                {
                    if (ni.OperationalStatus != OperationalStatus.Up) continue;
                    if (ni.NetworkInterfaceType == NetworkInterfaceType.Loopback) continue;

                    foreach (var addr in ni.GetIPProperties().UnicastAddresses)
                    {
                        if (addr.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                        {
                            var ip = addr.Address.GetAddressBytes();
                            var mask = addr.IPv4Mask?.GetAddressBytes();

                            if (mask != null)
                            {
                                var broadcast = new byte[4];
                                for (int i = 0; i < 4; i++)
                                {
                                    broadcast[i] = (byte)(ip[i] | (~mask[i] & 0xFF));
                                }
                                addresses.Add(new IPAddress(broadcast).ToString());
                            }
                        }
                    }
                }
            }
            catch { /* Ignore errors */ }

            return addresses;
        }

        private void StopListening()
        {
            isListening = false;
            cancellationTokenSource?.Cancel();

            try { broadcastListener?.Close(); } catch { }
            try { udpClient?.Close(); } catch { }

            broadcastListener?.Dispose();
            udpClient?.Dispose();
            cancellationTokenSource?.Dispose();
        }

        public void Dispose()
        {
            StopListening();
        }
    }

    public partial class VMSClientForm : Form
    {
        private Label titleLabel = new();
        private Label statusLabel = new();
        private PictureBox spinnerBox = new();
        private ProgressBar progressBar = new();
        private Button retryButton = new();
        private System.Windows.Forms.Timer spinnerTimer = new();
        private int spinnerAngle = 0;
        private HttpClient httpClient = new();
        private string foundServerUrl = "";

        public VMSClientForm()
        {
            InitializeComponent();
            httpClient.Timeout = TimeSpan.FromSeconds(1); // Fast timeout for LAN
        }

        private void InitializeComponent()
        {
            // Form setup
            Text = "VMS Client";
            Size = new Size(500, 350);
            StartPosition = FormStartPosition.CenterScreen;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            BackColor = Color.FromArgb(240, 248, 255); // Light blue background

            // Load custom icon
            try
            {
                string iconPath = Path.Combine(Application.StartupPath, "VMS.ico");
                if (File.Exists(iconPath))
                {
                    Icon = new Icon(iconPath);
                }
                else
                {
                    Icon = SystemIcons.Application;
                }
            }
            catch
            {
                Icon = SystemIcons.Application;
            }

            // Title Label
            titleLabel.Text = "VMS SYSTEM CLIENT";
            titleLabel.Font = new Font("Segoe UI", 18, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(25, 118, 210); // Blue color
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.Location = new Point(50, 30);
            titleLabel.Size = new Size(400, 40);
            Controls.Add(titleLabel);

            // Subtitle
            Label subtitleLabel = new();
            subtitleLabel.Text = "Automatic Server Connection";
            subtitleLabel.Font = new Font("Segoe UI", 10, FontStyle.Regular);
            subtitleLabel.ForeColor = Color.FromArgb(100, 100, 100);
            subtitleLabel.TextAlign = ContentAlignment.MiddleCenter;
            subtitleLabel.Location = new Point(50, 75);
            subtitleLabel.Size = new Size(400, 25);
            Controls.Add(subtitleLabel);

            // Spinner PictureBox
            spinnerBox.Location = new Point(225, 120);
            spinnerBox.Size = new Size(50, 50);
            spinnerBox.BackColor = Color.Transparent;
            spinnerBox.Paint += SpinnerBox_Paint;
            Controls.Add(spinnerBox);

            // Status Label
            statusLabel.Text = "Please wait while we connect you to the VMS SYSTEM";
            statusLabel.Font = new Font("Segoe UI", 11, FontStyle.Regular);
            statusLabel.ForeColor = Color.FromArgb(66, 66, 66);
            statusLabel.TextAlign = ContentAlignment.MiddleCenter;
            statusLabel.Location = new Point(50, 185);
            statusLabel.Size = new Size(400, 50);
            Controls.Add(statusLabel);

            // Progress Bar
            progressBar.Location = new Point(100, 250);
            progressBar.Size = new Size(300, 20);
            progressBar.Style = ProgressBarStyle.Marquee;
            progressBar.MarqueeAnimationSpeed = 50;
            progressBar.ForeColor = Color.FromArgb(25, 118, 210);
            Controls.Add(progressBar);

            // Retry Button (initially hidden)
            retryButton.Text = "Try Again";
            retryButton.Font = new Font("Segoe UI", 10, FontStyle.Regular);
            retryButton.BackColor = Color.FromArgb(25, 118, 210);
            retryButton.ForeColor = Color.White;
            retryButton.FlatStyle = FlatStyle.Flat;
            retryButton.FlatAppearance.BorderSize = 0;
            retryButton.Location = new Point(200, 280);
            retryButton.Size = new Size(100, 35);
            retryButton.Visible = false;
            retryButton.Click += RetryButton_Click;
            Controls.Add(retryButton);

            // Spinner Timer
            spinnerTimer.Interval = 50; // 50ms for smooth animation
            spinnerTimer.Tick += SpinnerTimer_Tick;
        }

        private void SpinnerTimer_Tick(object? sender, EventArgs e)
        {
            spinnerAngle += 10;
            if (spinnerAngle >= 360) spinnerAngle = 0;
            spinnerBox.Invalidate(); // Trigger repaint
        }

        private void SpinnerBox_Paint(object? sender, PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

            // Draw spinning circle
            using Pen pen = new(Color.FromArgb(25, 118, 210), 4)
            {
                StartCap = System.Drawing.Drawing2D.LineCap.Round,
                EndCap = System.Drawing.Drawing2D.LineCap.Round
            };

            // Draw arc that rotates
            Rectangle rect = new(5, 5, 40, 40);
            g.DrawArc(pen, rect, spinnerAngle, 270);
        }

        protected override async void OnShown(EventArgs e)
        {
            base.OnShown(e);
            spinnerTimer.Start();
            
            // Add a small delay to show the beautiful GUI
            await Task.Delay(1500);
            await StartServerDiscovery();
        }

        private async Task StartServerDiscovery()
        {
            try
            {
                UpdateStatus("Searching for VMS Server using UDP discovery...");

                // Method 1: Use UDP Service Discovery (Primary method)
                using var discovery = new VMSServiceDiscovery();

                UpdateStatus("Listening for VMS server announcements...");
                var discoveredServer = await discovery.DiscoverServerAsync(8000); // 8 second timeout

                if (discoveredServer != null)
                {
                    UpdateStatus($"Found VMS Server at {discoveredServer.Host}:{discoveredServer.Port}");
                    foundServerUrl = discoveredServer.Url;

                    // Verify server is actually accessible
                    if (await TestServer(foundServerUrl))
                    {
                        await ConnectToServer();
                        return;
                    }
                }

                // Method 2: Fallback to localhost check
                UpdateStatus("Checking localhost...");
                string[] localAddresses = [
                    "http://localhost:8080",
                    "http://127.0.0.1:8080"
                ];

                foreach (string url in localAddresses)
                {
                    UpdateStatus($"Checking {url.Replace("http://", "")}...");
                    if (await TestServer(url))
                    {
                        foundServerUrl = url;
                        await ConnectToServer();
                        return;
                    }
                }

                // Method 3: Network scan as last resort
                UpdateStatus("Scanning local network...");
                var networkServer = await ScanLocalNetwork();
                if (networkServer != null)
                {
                    foundServerUrl = networkServer;
                    await ConnectToServer();
                    return;
                }

                // Server not found
                ShowError("VMS Server not found on network",
                         "Please ensure:\n• VMS Server is running on C:\\VMS-PRODUCTION\n• You are connected to the same network\n• Firewall allows UDP ports 45454/45455 and TCP port 8080\n• Server service discovery is active");
            }
            catch (Exception ex)
            {
                ShowError("Connection Error", $"Network discovery error:\n{ex.Message}\n\nPlease check your network connection and try again.");
            }
        }

        private async Task<bool> TestServer(string url)
        {
            try
            {
                var response = await httpClient.GetAsync($"{url}/health");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string?> ScanLocalNetwork()
        {
            try
            {
                var networkBase = GetNetworkBase();
                if (string.IsNullOrEmpty(networkBase)) return null;

                UpdateStatus("Scanning network for VMS server...");

                // Common server IPs to check first
                string[] priorityIPs = [
                    $"{networkBase}.1",
                    $"{networkBase}.100",
                    $"{networkBase}.200",
                    $"{networkBase}.250"
                ];

                // Check priority IPs first
                foreach (var ip in priorityIPs)
                {
                    var url = $"http://{ip}:8080";
                    UpdateStatus($"Checking {ip}...");
                    if (await TestServer(url))
                    {
                        return url;
                    }
                }

                // If not found, do a broader scan
                var tasks = new List<Task<string?>>();
                for (int i = 1; i <= 254; i++)
                {
                    var ip = $"{networkBase}.{i}";
                    var url = $"http://{ip}:8080";
                    tasks.Add(TestServerWithResult(url));
                }

                var results = await Task.WhenAll(tasks);
                return results.FirstOrDefault(r => r != null);
            }
            catch
            {
                return null;
            }
        }

        private async Task<string?> TestServerWithResult(string url)
        {
            try
            {
                var response = await httpClient.GetAsync($"{url}/health");
                return response.IsSuccessStatusCode ? url : null;
            }
            catch
            {
                return null;
            }
        }

        private string GetNetworkBase()
        {
            try
            {
                foreach (NetworkInterface ni in NetworkInterface.GetAllNetworkInterfaces())
                {
                    if (ni.OperationalStatus == OperationalStatus.Up && 
                        ni.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                    {
                        foreach (var ip in ni.GetIPProperties().UnicastAddresses)
                        {
                            if (ip.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                            {
                                string ipStr = ip.Address.ToString();
                                if (!ipStr.StartsWith("127.") && !ipStr.StartsWith("169.254."))
                                {
                                    string[] parts = ipStr.Split('.');
                                    return $"{parts[0]}.{parts[1]}.{parts[2]}";
                                }
                            }
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private async Task ConnectToServer()
        {
            UpdateStatus("VMS Server found! Opening browser...");
            
            await Task.Delay(1000); // Brief pause for user to see success message

            try
            {
                string chromePath = FindChrome();
                if (!string.IsNullOrEmpty(chromePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = chromePath,
                        Arguments = $"--new-window --start-maximized \"{foundServerUrl}\"",
                        UseShellExecute = false
                    });
                }
                else
                {
                    // Fallback to default browser
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = foundServerUrl,
                        UseShellExecute = true
                    });
                }

                UpdateStatus("VMS System opened successfully!");
                spinnerTimer.Stop();
                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 100;

                await Task.Delay(2000);
                Close();
            }
            catch (Exception ex)
            {
                ShowError("Browser Error", $"Could not open browser: {ex.Message}");
            }
        }

        private static string FindChrome()
        {
            string[] chromePaths = [
                @"C:\Program Files\Google\Chrome\Application\chrome.exe",
                @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), 
                                      @"Google\Chrome\Application\chrome.exe")
            ];

            foreach (string path in chromePaths)
            {
                if (File.Exists(path))
                    return path;
            }

            return "";
        }

        private void UpdateStatus(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(UpdateStatus), message);
                return;
            }
            statusLabel.Text = message;
        }

        private void ShowError(string title, string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, string>(ShowError), title, message);
                return;
            }

            spinnerTimer.Stop();
            progressBar.Visible = false;
            statusLabel.Text = message;
            statusLabel.ForeColor = Color.FromArgb(211, 47, 47); // Red color
            retryButton.Visible = true;
        }

        private async void RetryButton_Click(object? sender, EventArgs e)
        {
            retryButton.Visible = false;
            progressBar.Visible = true;
            statusLabel.ForeColor = Color.FromArgb(66, 66, 66);
            spinnerTimer.Start();
            await StartServerDiscovery();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            spinnerTimer.Stop();
            httpClient.Dispose();
            base.OnFormClosed(e);
        }
    }

    public class Program
    {
        [STAThread]
        public static void Main()
        {
            Application.SetHighDpiMode(HighDpiMode.SystemAware);
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new VMSClientForm());
        }
    }
}
