===============================================
VMS CLIENT - .NET 8 LTS INSTALLATION GUIDE
===============================================

SYSTEM REQUIREMENTS:
- Windows 10 version 1903 or later, Windows 11
- Microsoft Edge WebView2 Runtime (auto-installed if missing)
- Network access to VMS server

INSTALLATION STEPS:
1. Copy this entire folder to your desired location
2. Run "Launch-VMS-Client.bat" or "VMS-Client-NET8.exe"
3. The application will automatically discover VMS servers

FIRST RUN:
- The client will scan your network for VMS servers
- If found, it will connect to the best available server
- If no servers found, use "Connect to Server" dialog

MANUAL CONNECTION:
- Click "File" → "Connect to Server..."
- Enter server details: ************:8080 (production server)
- Click "Test & Add" then "Connect"

FEATURES:
✓ Automatic server discovery
✓ Network scanning and health checks
✓ WebView2 embedded VMS interface
✓ Connection management and caching
✓ Windows 10/11 native integration

TROUBLESHOOTING:
- If WebView2 error: Download from https://developer.microsoft.com/en-us/microsoft-edge/webview2/
- If connection fails: Check network and firewall settings
- If discovery fails: Try manual connection with known server IP

CONFIGURATION:
- Settings stored in: %APPDATA%\VMS-Client\config.json
- Logs available in: %APPDATA%\VMS-Client\logs\

SUPPORT:
Contact your VMS system administrator for network or server issues.

Version: 1.0.0
Built: 2025-08-15
.NET Version: 8.0 LTS
