const dgram = require('dgram');

console.log('=== VMS Client Discovery Test ===');
console.log('This tool tests if the VMS client can discover the VMS server');
console.log('');

// Test 1: Listen for server broadcasts
console.log('Test 1: Listening for VMS server broadcasts on port 45454...');
const broadcastListener = dgram.createSocket('udp4');

broadcastListener.on('error', (err) => {
  console.log('❌ Broadcast listener error:', err.message);
  broadcastListener.close();
});

broadcastListener.on('message', (msg, rinfo) => {
  try {
    const data = JSON.parse(msg.toString());
    if (data.type === 'VMS_SERVICE_ANNOUNCEMENT') {
      console.log('✅ FOUND VMS SERVER!');
      console.log('   Server IP:', data.localIPs[0]);
      console.log('   Server Port:', data.service.port);
      console.log('   Server Version:', data.service.version);
      console.log('   Full URL: http://' + data.localIPs[0] + ':' + data.service.port);
      console.log('');
    }
  } catch (error) {
    console.log('📦 Received non-VMS broadcast from', rinfo.address);
  }
});

broadcastListener.bind(45454, () => {
  console.log('✅ Listening for broadcasts on port 45454');
});

// Test 2: Send discovery request
setTimeout(() => {
  console.log('');
  console.log('Test 2: Sending discovery request to server...');
  
  const discoveryClient = dgram.createSocket('udp4');
  const request = JSON.stringify({ type: 'VMS_DISCOVERY_REQUEST' });
  
  // Try broadcasting the request
  discoveryClient.send(request, 45455, '***************', (err) => {
    if (err) {
      console.log('❌ Failed to send discovery request:', err.message);
    } else {
      console.log('✅ Discovery request sent');
    }
    discoveryClient.close();
  });
}, 3000);

// Test 3: Try direct connection test
setTimeout(() => {
  console.log('');
  console.log('Test 3: Testing direct HTTP connection...');
  
  const http = require('http');
  
  // Try common VMS server IPs on the network
  const testIPs = ['************', '************', '************'];
  
  testIPs.forEach(ip => {
    const req = http.get(`http://${ip}:8080/health`, { timeout: 2000 }, (res) => {
      console.log(`✅ VMS Server found at http://${ip}:8080 (Status: ${res.statusCode})`);
    });
    
    req.on('error', (err) => {
      console.log(`❌ No VMS server at http://${ip}:8080`);
    });
    
    req.on('timeout', () => {
      console.log(`⏱️  Timeout connecting to http://${ip}:8080`);
      req.destroy();
    });
  });
}, 6000);

// Cleanup after 15 seconds
setTimeout(() => {
  console.log('');
  console.log('=== Test Complete ===');
  console.log('If you saw "FOUND VMS SERVER!" above, auto-discovery is working.');
  console.log('If not, there may be a firewall or network issue.');
  console.log('');
  broadcastListener.close();
  process.exit(0);
}, 15000);
