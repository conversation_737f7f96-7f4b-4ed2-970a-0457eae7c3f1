using Microsoft.Extensions.Logging;
using System.ComponentModel;
using VMS.Client.Models;
using VMS.Client.Services;

namespace VMS.Client.Forms;

public partial class ServerDiscoveryForm : Form
{
    private readonly IServerDiscoveryService _discoveryService;
    private readonly IConfigurationService _configService;
    
    private ListView _serverListView;
    private Button _discoverButton;
    private Button _connectButton;
    private Button _cancelButton;
    private Button _addManualButton;
    private TextBox _manualHostTextBox;
    private NumericUpDown _manualPortNumeric;
    private Label _statusLabel;
    private ProgressBar _progressBar;
    
    public VmsServerInfo? SelectedServer { get; private set; }

    public ServerDiscoveryForm(IServerDiscoveryService discoveryService, IConfigurationService configService)
    {
        _discoveryService = discoveryService;
        _configService = configService;
        
        InitializeComponent();
        SetupEventHandlers();
        LoadSavedServers();
    }

    private void InitializeComponent()
    {
        SuspendLayout();
        
        Text = "Connect to VMS Server";
        Size = new Size(600, 450);
        StartPosition = FormStartPosition.CenterParent;
        FormBorderStyle = FormBorderStyle.FixedDialog;
        MaximizeBox = false;
        MinimizeBox = false;
        
        // Server list
        var serverLabel = new Label
        {
            Text = "Available VMS Servers:",
            Location = new Point(12, 12),
            Size = new Size(200, 23),
            AutoSize = true
        };
        
        _serverListView = new ListView
        {
            Location = new Point(12, 35),
            Size = new Size(560, 200),
            View = View.Details,
            FullRowSelect = true,
            GridLines = true,
            MultiSelect = false
        };
        
        _serverListView.Columns.Add("Status", 60);
        _serverListView.Columns.Add("Server", 150);
        _serverListView.Columns.Add("Port", 60);
        _serverListView.Columns.Add("Version", 80);
        _serverListView.Columns.Add("Ping", 60);
        _serverListView.Columns.Add("Last Seen", 120);
        
        // Manual connection
        var manualLabel = new Label
        {
            Text = "Manual Connection:",
            Location = new Point(12, 250),
            Size = new Size(150, 23),
            AutoSize = true
        };
        
        var hostLabel = new Label
        {
            Text = "Host:",
            Location = new Point(12, 280),
            Size = new Size(40, 23),
            AutoSize = true
        };
        
        _manualHostTextBox = new TextBox
        {
            Location = new Point(55, 277),
            Size = new Size(200, 23),
            Text = "************"
        };
        
        var portLabel = new Label
        {
            Text = "Port:",
            Location = new Point(270, 280),
            Size = new Size(35, 23),
            AutoSize = true
        };
        
        _manualPortNumeric = new NumericUpDown
        {
            Location = new Point(310, 277),
            Size = new Size(80, 23),
            Minimum = 1,
            Maximum = 65535,
            Value = 8080
        };
        
        _addManualButton = new Button
        {
            Text = "Test & Add",
            Location = new Point(400, 276),
            Size = new Size(80, 25)
        };
        
        // Status
        _statusLabel = new Label
        {
            Text = "Ready",
            Location = new Point(12, 320),
            Size = new Size(400, 23),
            AutoSize = true
        };
        
        _progressBar = new ProgressBar
        {
            Location = new Point(12, 345),
            Size = new Size(560, 23),
            Style = ProgressBarStyle.Marquee,
            Visible = false
        };
        
        // Buttons
        _discoverButton = new Button
        {
            Text = "Discover Servers",
            Location = new Point(12, 380),
            Size = new Size(120, 30)
        };
        
        _connectButton = new Button
        {
            Text = "Connect",
            Location = new Point(380, 380),
            Size = new Size(90, 30),
            DialogResult = DialogResult.OK,
            Enabled = false
        };
        
        _cancelButton = new Button
        {
            Text = "Cancel",
            Location = new Point(480, 380),
            Size = new Size(90, 30),
            DialogResult = DialogResult.Cancel
        };
        
        // Add controls
        Controls.AddRange(new Control[]
        {
            serverLabel, _serverListView,
            manualLabel, hostLabel, _manualHostTextBox, portLabel, _manualPortNumeric, _addManualButton,
            _statusLabel, _progressBar,
            _discoverButton, _connectButton, _cancelButton
        });
        
        AcceptButton = _connectButton;
        CancelButton = _cancelButton;
        
        ResumeLayout(false);
        PerformLayout();
    }

    private void SetupEventHandlers()
    {
        _discoveryService.StatusChanged += OnDiscoveryStatusChanged;
        _discoveryService.ServersDiscovered += OnServersDiscovered;
        
        _discoverButton.Click += OnDiscoverClick;
        _addManualButton.Click += OnAddManualClick;
        _connectButton.Click += OnConnectClick;
        _serverListView.SelectedIndexChanged += OnServerSelectionChanged;
        _serverListView.DoubleClick += OnServerDoubleClick;
    }

    private void LoadSavedServers()
    {
        var savedServers = _configService.GetSavedServers();
        foreach (var server in savedServers)
        {
            AddServerToList(server);
        }
        
        // Also load cached servers from discovery service
        var cachedServers = _discoveryService.GetCachedServers();
        foreach (var server in cachedServers)
        {
            if (!savedServers.Any(s => s.Host == server.Host && s.Port == server.Port))
            {
                AddServerToList(server);
            }
        }
    }

    private void AddServerToList(VmsServerInfo server)
    {
        var statusIcon = server.IsHealthy ? "✅" : "❓";
        var pingText = server.Ping?.ToString() + "ms" ?? "N/A";
        var lastSeenText = server.LastSeen.ToString("HH:mm:ss");
        
        var item = new ListViewItem(new[]
        {
            statusIcon,
            server.Host,
            server.Port.ToString(),
            server.Version,
            pingText,
            lastSeenText
        })
        {
            Tag = server
        };
        
        _serverListView.Items.Add(item);
    }

    private async void OnDiscoverClick(object? sender, EventArgs e)
    {
        try
        {
            _discoverButton.Enabled = false;
            _progressBar.Visible = true;
            _statusLabel.Text = "Discovering servers...";
            
            _serverListView.Items.Clear();
            
            var result = await _discoveryService.DiscoverServersAsync();
            
            _statusLabel.Text = result.Success 
                ? $"Found {result.Servers.Count} server(s) in {result.Duration.TotalSeconds:F1}s"
                : "No servers found";
        }
        catch (Exception ex)
        {
            _statusLabel.Text = $"Discovery failed: {ex.Message}";
        }
        finally
        {
            _discoverButton.Enabled = true;
            _progressBar.Visible = false;
        }
    }

    private async void OnAddManualClick(object? sender, EventArgs e)
    {
        var host = _manualHostTextBox.Text.Trim();
        var port = (int)_manualPortNumeric.Value;
        
        if (string.IsNullOrEmpty(host))
        {
            MessageBox.Show("Please enter a host name or IP address.", "Invalid Input", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }
        
        try
        {
            _addManualButton.Enabled = false;
            _statusLabel.Text = $"Testing connection to {host}:{port}...";
            
            var server = await _discoveryService.TestServerAsync(host, port);
            
            if (server != null)
            {
                AddServerToList(server);
                _configService.SaveServer(server);
                _statusLabel.Text = $"Successfully added {host}:{port}";
            }
            else
            {
                _statusLabel.Text = $"Failed to connect to {host}:{port}";
                MessageBox.Show($"Could not connect to VMS server at {host}:{port}.\n\nPlease check the address and try again.", 
                    "Connection Failed", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        catch (Exception ex)
        {
            _statusLabel.Text = $"Connection test failed: {ex.Message}";
        }
        finally
        {
            _addManualButton.Enabled = true;
        }
    }

    private void OnConnectClick(object? sender, EventArgs e)
    {
        if (_serverListView.SelectedItems.Count > 0)
        {
            SelectedServer = (VmsServerInfo)_serverListView.SelectedItems[0].Tag;
        }
    }

    private void OnServerSelectionChanged(object? sender, EventArgs e)
    {
        _connectButton.Enabled = _serverListView.SelectedItems.Count > 0;
    }

    private void OnServerDoubleClick(object? sender, EventArgs e)
    {
        if (_serverListView.SelectedItems.Count > 0)
        {
            SelectedServer = (VmsServerInfo)_serverListView.SelectedItems[0].Tag;
            DialogResult = DialogResult.OK;
            Close();
        }
    }

    private void OnDiscoveryStatusChanged(object? sender, string status)
    {
        if (InvokeRequired)
        {
            Invoke(() => OnDiscoveryStatusChanged(sender, status));
            return;
        }
        
        _statusLabel.Text = status;
    }

    private void OnServersDiscovered(object? sender, DiscoveryResult result)
    {
        if (InvokeRequired)
        {
            Invoke(() => OnServersDiscovered(sender, result));
            return;
        }
        
        _serverListView.Items.Clear();
        
        foreach (var server in result.Servers)
        {
            AddServerToList(server);
        }
        
        // Auto-select best server
        if (result.BestServer != null && _serverListView.Items.Count > 0)
        {
            var bestItem = _serverListView.Items.Cast<ListViewItem>()
                .FirstOrDefault(item => ((VmsServerInfo)item.Tag).Url == result.BestServer.Url);
            
            if (bestItem != null)
            {
                bestItem.Selected = true;
                bestItem.EnsureVisible();
            }
        }
    }

    protected override void OnFormClosing(FormClosingEventArgs e)
    {
        _discoveryService.StatusChanged -= OnDiscoveryStatusChanged;
        _discoveryService.ServersDiscovered -= OnServersDiscovered;
        base.OnFormClosing(e);
    }
}
