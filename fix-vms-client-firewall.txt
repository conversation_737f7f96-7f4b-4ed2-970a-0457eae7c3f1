REM ===================================================================
REM VMS CLIENT FIREWALL FIX
REM Run this command as Administrator to allow VMS client auto-discovery
REM ===================================================================

netsh advfirewall firewall add rule name="VMS Client Discovery" dir=in action=allow protocol=UDP localport=45454

REM ===================================================================
REM EXPLANATION:
REM This command creates a Windows Firewall rule that allows the VMS 
REM client to receive UDP broadcasts on port 45454 from the VMS server.
REM This enables auto-discovery to work properly.
REM ===================================================================
