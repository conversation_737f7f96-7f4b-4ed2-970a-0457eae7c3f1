using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Net.NetworkInformation;
using VMS.Client.Models;

namespace VMS.Client.Services;

public class ServerDiscoveryService : IServerDiscoveryService
{
    private readonly ILogger<ServerDiscoveryService> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly INetworkService _networkService;
    private readonly ConcurrentDictionary<string, VmsServerInfo> _discoveredServers = new();
    private readonly SemaphoreSlim _discoveryLock = new(1, 1);

    public event EventHandler<DiscoveryResult>? ServersDiscovered;
    public event EventHandler<string>? StatusChanged;

    public ServerDiscoveryService(
        ILogger<ServerDiscoveryService> logger,
        IHttpClientFactory httpClientFactory,
        INetworkService networkService)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _networkService = networkService;
    }

    public async Task<DiscoveryResult> DiscoverServersAsync(CancellationToken cancellationToken = default)
    {
        await _discoveryLock.WaitAsync(cancellationToken);
        
        try
        {
            var stopwatch = Stopwatch.StartNew();
            _discoveredServers.Clear();
            
            OnStatusChanged("Starting VMS server discovery...");
            _logger.LogInformation("Starting VMS server discovery");

            // Method 1: Try localhost first (fastest)
            await TryLocalhostAsync(cancellationToken);
            
            // Method 2: Try known production server IP
            await TryKnownServersAsync(cancellationToken);
            
            // Method 3: Try common network ranges
            await TryNetworkRangesAsync(cancellationToken);
            
            // Method 4: Try port scanning on local network
            await TryPortScanningAsync(cancellationToken);

            stopwatch.Stop();
            
            var result = new DiscoveryResult
            {
                Servers = _discoveredServers.Values.ToList(),
                BestServer = SelectBestServer(_discoveredServers.Values.ToList()),
                DiscoveryMethod = "comprehensive-scan",
                Duration = stopwatch.Elapsed,
                Message = $"Found {_discoveredServers.Count} VMS server(s) in {stopwatch.Elapsed.TotalSeconds:F1}s"
            };

            OnStatusChanged(result.Message);
            OnServersDiscovered(result);
            
            _logger.LogInformation("Discovery completed: {ServerCount} servers found in {Duration}ms", 
                result.Servers.Count, stopwatch.ElapsedMilliseconds);
            
            return result;
        }
        finally
        {
            _discoveryLock.Release();
        }
    }

    private async Task TryLocalhostAsync(CancellationToken cancellationToken)
    {
        OnStatusChanged("Checking localhost...");
        var commonPorts = new[] { 8080, 3000, 8000, 8081, 3001, 5000, 5001 };
        
        var tasks = commonPorts.Select(port => 
            TestServerAsync("localhost", port, cancellationToken));
        
        await Task.WhenAll(tasks);
    }

    private async Task TryKnownServersAsync(CancellationToken cancellationToken)
    {
        OnStatusChanged("Checking known servers...");
        var knownServers = new[]
        {
            ("************", 8080), // Current production server
            ("************", 8080), // Alternative server IP
            ("127.0.0.1", 8080),
            ("*************", 8080),
            ("*************", 8080)
        };

        var tasks = knownServers.Select(server => 
            TestServerAsync(server.Item1, server.Item2, cancellationToken));
        
        await Task.WhenAll(tasks);
    }

    private async Task TryNetworkRangesAsync(CancellationToken cancellationToken)
    {
        OnStatusChanged("Scanning network ranges...");
        
        try
        {
            var networkRanges = await _networkService.GetLocalNetworkRangesAsync();
            var tasks = new List<Task>();

            foreach (var range in networkRanges.Take(3)) // Limit to 3 ranges for performance
            {
                // Parse network range and scan common IPs
                var baseIp = range.Split('.').Take(3).ToArray();
                if (baseIp.Length == 3)
                {
                    var commonLastOctets = new[] { 1, 100, 200, 216, 209 };
                    
                    foreach (var lastOctet in commonLastOctets)
                    {
                        var ip = $"{string.Join(".", baseIp)}.{lastOctet}";
                        tasks.Add(TestServerAsync(ip, 8080, cancellationToken));
                        
                        if (tasks.Count > 20) break; // Limit concurrent tasks
                    }
                }
            }

            if (tasks.Count > 0)
            {
                await Task.WhenAll(tasks);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Network range scanning failed");
        }
    }

    private async Task TryPortScanningAsync(CancellationToken cancellationToken)
    {
        OnStatusChanged("Port scanning...");
        
        var commonIPs = _networkService.GetCommonServerIPs();
        var commonPorts = new[] { 8080, 8000, 3000, 5000 };
        
        var tasks = new List<Task>();
        
        foreach (var ip in commonIPs.Take(10)) // Limit IPs
        {
            foreach (var port in commonPorts)
            {
                tasks.Add(TestServerAsync(ip, port, cancellationToken));
            }
        }
        
        if (tasks.Count > 0)
        {
            await Task.WhenAll(tasks);
        }
    }

    public async Task<VmsServerInfo?> TestServerAsync(string host, int port, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(3);
            
            var url = $"http://{host}:{port}";
            var healthUrl = $"{url}/api/health";
            
            var response = await httpClient.GetAsync(healthUrl, cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var healthInfo = System.Text.Json.JsonSerializer.Deserialize<ServerHealthInfo>(content, 
                    new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                
                if (healthInfo != null && IsVmsServer(healthInfo))
                {
                    var serverInfo = new VmsServerInfo
                    {
                        ServiceName = healthInfo.ServiceName ?? "VMS-Server",
                        Host = host,
                        Port = port,
                        Version = healthInfo.Version ?? "Unknown",
                        Timestamp = healthInfo.Timestamp,
                        Capabilities = healthInfo.Capabilities ?? new List<string>(),
                        Ping = (int)stopwatch.ElapsedMilliseconds,
                        LastSeen = DateTime.Now,
                        DiscoveryMethod = "http-health-check",
                        IsHealthy = true,
                        Status = healthInfo.Status ?? "healthy"
                    };
                    
                    _discoveredServers.TryAdd($"{host}:{port}", serverInfo);
                    
                    _logger.LogInformation("VMS server discovered: {Url} ({Ping}ms)", url, serverInfo.Ping);
                    OnStatusChanged($"Found VMS server at {url} ({serverInfo.Ping}ms)");
                    
                    return serverInfo;
                }
            }
        }
        catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
        {
            // Silently ignore connection failures during discovery
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error testing server {Host}:{Port}", host, port);
        }
        
        return null;
    }

    private static bool IsVmsServer(ServerHealthInfo healthInfo)
    {
        return healthInfo.ServiceName?.Contains("vms", StringComparison.OrdinalIgnoreCase) == true ||
               healthInfo.ServiceName?.Contains("VMS", StringComparison.Ordinal) == true ||
               healthInfo.Status?.Equals("healthy", StringComparison.OrdinalIgnoreCase) == true;
    }

    private static VmsServerInfo? SelectBestServer(List<VmsServerInfo> servers)
    {
        if (servers.Count == 0) return null;
        
        // Prefer localhost
        var localhost = servers.FirstOrDefault(s => 
            s.Host.Equals("localhost", StringComparison.OrdinalIgnoreCase) || 
            s.Host == "127.0.0.1");
        if (localhost != null) return localhost;
        
        // Prefer known production server
        var production = servers.FirstOrDefault(s => s.Host == "************");
        if (production != null) return production;
        
        // Otherwise, select by lowest ping
        return servers.Where(s => s.Ping.HasValue)
                     .OrderBy(s => s.Ping)
                     .FirstOrDefault() ?? servers.First();
    }

    public List<VmsServerInfo> GetCachedServers()
    {
        return _discoveredServers.Values.ToList();
    }

    public void ClearCache()
    {
        _discoveredServers.Clear();
        OnStatusChanged("Discovery cache cleared");
    }

    protected virtual void OnServersDiscovered(DiscoveryResult result)
    {
        ServersDiscovered?.Invoke(this, result);
    }

    protected virtual void OnStatusChanged(string status)
    {
        StatusChanged?.Invoke(this, status);
    }
}
