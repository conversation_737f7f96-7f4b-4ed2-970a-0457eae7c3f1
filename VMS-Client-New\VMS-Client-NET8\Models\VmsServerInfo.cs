namespace VMS.Client.Models;

public class VmsServerInfo
{
    public string ServiceName { get; set; } = string.Empty;
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; }
    public string Version { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public List<string> Capabilities { get; set; } = new();
    public string Url => $"http://{Host}:{Port}";
    public int? Ping { get; set; }
    public DateTime LastSeen { get; set; }
    public string DiscoveryMethod { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public string Status { get; set; } = string.Empty;

    public override string ToString()
    {
        var pingText = Ping.HasValue ? $" ({Ping}ms)" : "";
        return $"{ServiceName} - {Url}{pingText}";
    }

    public string GetDisplayText()
    {
        var status = IsHealthy ? "✅" : "❌";
        var pingText = Ping.HasValue ? $" - {Ping}ms" : "";
        return $"{status} {Host}:{Port}{pingText} - {Version}";
    }
}

public class DiscoveryResult
{
    public List<VmsServerInfo> Servers { get; set; } = new();
    public VmsServerInfo? BestServer { get; set; }
    public string DiscoveryMethod { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public bool Success => Servers.Count > 0;
    public string Message { get; set; } = string.Empty;
}

public class ServerHealthInfo
{
    public string Status { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public TimeSpan Uptime { get; set; }
    public string Version { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public Dictionary<string, object> Memory { get; set; } = new();
    public List<string> Capabilities { get; set; } = new();
}
