@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-secondary;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-md;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Loading animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0; 
    transform: translateY(-10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Connection status indicator */
.connection-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.connection-indicator.connected {
  @apply bg-green-100 text-green-800 border border-green-200;
}

.connection-indicator.connecting {
  @apply bg-yellow-100 text-yellow-800 border border-yellow-200;
}

.connection-indicator.disconnected {
  @apply bg-red-100 text-red-800 border border-red-200;
}

.connection-indicator .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.connection-indicator.connected .status-dot {
  @apply bg-green-500;
}

.connection-indicator.connecting .status-dot {
  @apply bg-yellow-500;
  animation: pulse 2s infinite;
}

.connection-indicator.disconnected .status-dot {
  @apply bg-red-500;
}

/* Form styles */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply text-sm font-medium text-foreground;
}

.form-input {
  @apply w-full px-3 py-2 border border-input bg-background text-foreground rounded-md;
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.form-select {
  @apply form-input appearance-none bg-no-repeat bg-right;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 32px;
}

.form-button {
  @apply px-4 py-2 bg-primary text-primary-foreground rounded-md font-medium;
  @apply hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  @apply transition-colors duration-200;
}

.form-button.secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
}

.form-button.destructive {
  @apply bg-destructive text-destructive-foreground hover:bg-destructive/90;
}

/* Table styles */
.data-table {
  @apply w-full border-collapse border border-border rounded-lg overflow-hidden;
}

.data-table th {
  @apply bg-muted text-muted-foreground font-medium text-left px-4 py-3 border-b border-border;
}

.data-table td {
  @apply px-4 py-3 border-b border-border;
}

.data-table tr:hover {
  @apply bg-muted/50;
}

/* Status badges */
.status-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.status-badge.success {
  @apply bg-green-100 text-green-800;
}

.status-badge.warning {
  @apply bg-yellow-100 text-yellow-800;
}

.status-badge.error {
  @apply bg-red-100 text-red-800;
}

.status-badge.info {
  @apply bg-blue-100 text-blue-800;
}

/* Card styles */
.card {
  @apply bg-card text-card-foreground border border-border rounded-lg shadow-sm;
}

.card-header {
  @apply px-6 py-4 border-b border-border;
}

.card-title {
  @apply text-lg font-semibold;
}

.card-content {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-border;
}
