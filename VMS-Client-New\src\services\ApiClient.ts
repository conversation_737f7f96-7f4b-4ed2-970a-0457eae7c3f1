/**
 * Dynamic VMS API Client with automatic server discovery and connection management
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { serverDiscovery, VMSServerInfo } from './ServerDiscovery';

export interface ApiClientConfig {
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
}

class DynamicApiClient {
  private axiosInstance: AxiosInstance | null = null;
  private currentServer: VMSServerInfo | null = null;
  private config: Required<ApiClientConfig>;
  private sessionId: string | null = null;
  private isInitialized = false;

  constructor(config: ApiClientConfig = {}) {
    this.config = {
      timeout: config.timeout || 15000,
      retryAttempts: config.retryAttempts || 3,
      retryDelay: config.retryDelay || 1000,
    };
  }

  /**
   * Initialize the API client with server discovery
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized && this.axiosInstance) {
      return true;
    }

    try {
      // Discover VMS servers
      const discovery = await serverDiscovery.discoverServers();
      
      if (!discovery.bestServer) {
        throw new Error('No VMS servers found on the network');
      }

      this.currentServer = discovery.bestServer;
      this.createAxiosInstance();
      this.isInitialized = true;

      console.log(`✅ API Client initialized with server: ${this.currentServer.url}`);
      return true;

    } catch (error) {
      console.error('❌ Failed to initialize API client:', error);
      return false;
    }
  }

  /**
   * Create axios instance with current server configuration
   */
  private createAxiosInstance(): void {
    if (!this.currentServer) {
      throw new Error('No server configured');
    }

    this.axiosInstance = axios.create({
      baseURL: `${this.currentServer.url}/api`,
      timeout: this.config.timeout,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Add session ID if available
        if (this.sessionId) {
          config.headers['x-session-id'] = this.sessionId;
        }
        
        // Add timestamp for cache busting
        config.headers['X-Request-Time'] = Date.now().toString();
        
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle server connection errors
        if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
          console.warn('Server connection lost, attempting to rediscover...');
          
          if (!originalRequest._retry) {
            originalRequest._retry = true;
            
            // Try to rediscover servers
            const success = await this.rediscoverServer();
            if (success) {
              // Retry the original request with new server
              return this.axiosInstance!.request(originalRequest);
            }
          }
        }

        // Handle authentication errors
        if (error.response?.status === 401) {
          this.sessionId = null;
          // Emit authentication error event
          window.dispatchEvent(new CustomEvent('auth-error', { 
            detail: { error: 'Authentication required' } 
          }));
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Rediscover servers and reconnect
   */
  private async rediscoverServer(): Promise<boolean> {
    try {
      const discovery = await serverDiscovery.discoverServers();
      
      if (discovery.bestServer && discovery.bestServer.url !== this.currentServer?.url) {
        this.currentServer = discovery.bestServer;
        this.createAxiosInstance();
        console.log(`🔄 Reconnected to server: ${this.currentServer.url}`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to rediscover servers:', error);
      return false;
    }
  }

  /**
   * Set session ID for authenticated requests
   */
  setSessionId(sessionId: string | null): void {
    this.sessionId = sessionId;
  }

  /**
   * Get current server info
   */
  getCurrentServer(): VMSServerInfo | null {
    return this.currentServer;
  }

  /**
   * Make a GET request
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    await this.ensureInitialized();
    const response = await this.axiosInstance!.get(url, config);
    return this.formatResponse(response);
  }

  /**
   * Make a POST request
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    await this.ensureInitialized();
    const response = await this.axiosInstance!.post(url, data, config);
    return this.formatResponse(response);
  }

  /**
   * Make a PUT request
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    await this.ensureInitialized();
    const response = await this.axiosInstance!.put(url, data, config);
    return this.formatResponse(response);
  }

  /**
   * Make a DELETE request
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    await this.ensureInitialized();
    const response = await this.axiosInstance!.delete(url, config);
    return this.formatResponse(response);
  }

  /**
   * Make a PATCH request
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    await this.ensureInitialized();
    const response = await this.axiosInstance!.patch(url, data, config);
    return this.formatResponse(response);
  }

  /**
   * Ensure the client is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized || !this.axiosInstance) {
      const success = await this.initialize();
      if (!success) {
        throw new Error('Failed to initialize API client');
      }
    }
  }

  /**
   * Format axios response to our standard format
   */
  private formatResponse<T>(response: AxiosResponse<T>): ApiResponse<T> {
    return {
      data: response.data,
      status: response.status,
      message: response.statusText,
    };
  }

  /**
   * Test connection to current server
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.get('/health');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get server health information
   */
  async getServerHealth(): Promise<any> {
    const response = await this.get('/health');
    return response.data;
  }
}

// Create singleton instance
export const apiClient = new DynamicApiClient();

// Export for use in components
export default apiClient;
